{"name": "braintrust-otel-example", "version": "1.0.0", "description": "Example using BraintrustSpanProcessor with OpenTelemetry NodeSDK", "main": "nodesdk_example.ts", "scripts": {"start": "tsx nodesdk_example.ts"}, "dependencies": {"@traceloop/instrumentation-openai": "^0.14.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/sdk-trace-base": "^2.0.1", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.36.0", "openai": "^4.73.1", "tsx": "^4.19.2"}, "devDependencies": {"@types/node": "^20.10.5", "typescript": "^5.4.4"}}