#!/usr/bin/env tsx

import { initDataset } from "braintrust";

async function main() {
  console.log("Creating a simple dataset example...\n");

  // Method 1: Create a dataset using the options object syntax
  const dataset = initDataset({
    project: "typescript-examples",
    dataset: "simple-qa-dataset",
    description: "A simple question-answer dataset for demonstration",
  });

  console.log("Dataset created!");
  console.log("Dataset name:", await dataset.name);
  console.log("Dataset ID:", await dataset.id);
  console.log("Project:", (await dataset.project).name);
  console.log();

  // Add some sample records to the dataset
  console.log("Adding sample records...");

  // Record 1: Simple math question
  const record1Id = dataset.insert({
    input: "What is 2 + 2?",
    expected: "4",
    tags: ["math", "basic"],
    metadata: {
      difficulty: "easy",
      category: "arithmetic",
    },
  });

  // Record 2: Geography question
  const record2Id = dataset.insert({
    input: "What is the capital of France?",
    expected: "Paris",
    tags: ["geography", "capitals"],
    metadata: {
      difficulty: "easy",
      category: "world-knowledge",
    },
  });

  // Record 3: Science question
  const record3Id = dataset.insert({
    input: "What is the chemical symbol for water?",
    expected: "H2O",
    tags: ["science", "chemistry"],
    metadata: {
      difficulty: "medium",
      category: "chemistry",
    },
  });

  console.log("Added record 1 with ID:", record1Id);
  console.log("Added record 2 with ID:", record2Id);
  console.log("Added record 3 with ID:", record3Id);
  console.log();

  // Method 2: Alternative syntax (legacy form)
  console.log("Creating another dataset using legacy syntax...");
  const dataset2 = initDataset("typescript-examples", {
    dataset: "another-dataset",
    description: "Another dataset using the legacy syntax",
  });

  console.log("Second dataset created!");
  console.log("Dataset name:", await dataset2.name);
  console.log();

  // Add a record to the second dataset
  const record4Id = dataset2.insert({
    input: "What color is the sky?",
    expected: "Blue",
    tags: ["general-knowledge"],
    metadata: {
      difficulty: "easy",
      category: "observation",
    },
  });

  console.log("Added record to second dataset with ID:", record4Id);
  console.log();

  console.log("Example completed! Check your Braintrust dashboard to see the datasets.");
}

main().catch(console.error);
