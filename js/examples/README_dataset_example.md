# Dataset Example - TypeScript SDK

This example demonstrates the simplest way to use `initDataset` with the Braintrust TypeScript SDK.

## What this example shows

- How to create a dataset using `initDataset`
- Two different syntax options for creating datasets
- How to add records to a dataset using the `insert` method
- The structure of dataset records (input, expected, tags, metadata)

## Prerequisites

1. Install the Braintrust SDK:
   ```bash
   npm install braintrust
   ```

2. Set your Braintrust API key as an environment variable:
   ```bash
   export BRAINTRUST_API_KEY=your_api_key_here
   ```

## Running the example

```bash
# From the js/examples directory
npx tsx dataset_example.ts

# Or if you have tsx installed globally
tsx dataset_example.ts
```

## Code explanation

### Creating a dataset

The example shows two ways to create a dataset:

**Method 1: Options object syntax (recommended)**
```typescript
const dataset = initDataset({
  project: "typescript-examples",
  dataset: "simple-qa-dataset", 
  description: "A simple question-answer dataset for demonstration",
});
```

**Method 2: Legacy syntax**
```typescript
const dataset = initDataset("typescript-examples", {
  dataset: "another-dataset",
  description: "Another dataset using the legacy syntax",
});
```

### Adding records

Use the `insert` method to add records to your dataset:

```typescript
const recordId = dataset.insert({
  input: "What is 2 + 2?",           // The input/question
  expected: "4",                     // The expected output/answer
  tags: ["math", "basic"],           // Optional tags for filtering
  metadata: {                        // Optional metadata
    difficulty: "easy",
    category: "arithmetic",
  },
});
```

### Dataset record structure

Each record can contain:
- `input`: The input data (required) - can be any JSON-serializable value
- `expected`: The expected output (optional) - can be any JSON-serializable value  
- `tags`: Array of strings for categorization (optional)
- `metadata`: Object with additional information (optional)
- `id`: Custom ID for the record (optional, auto-generated if not provided)

## What happens when you run this

1. Creates a dataset called "simple-qa-dataset" in the "typescript-examples" project
2. Adds 3 sample question-answer records to the dataset
3. Creates a second dataset using the legacy syntax
4. Adds 1 more record to the second dataset
5. Prints information about the datasets and record IDs

You can then view these datasets in your Braintrust dashboard to see the records that were added.

## Next steps

- Try modifying the input/expected values
- Add your own custom metadata fields
- Experiment with different tag combinations
- Use the dataset in evaluations with the `Eval` function
