interactions:
  - request:
      body:
        '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","instructions":"Just
        the number please"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "89"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RTW27jMAz8zykEfTcLW3FjO0foFYqFQUt0oq0sGRJVtChy94XlR+zd9idwhuRw
          OKS+DoxxrfiFcY9haM7VqZQdnts2l1VeV5XKTwrb/FmovD6VmMlzJ7DsSlAnUWTP/GkkcO0flLSQ
          OBtwwqVHIFQNjLG8LKqiqvLzOcUCAcUw1kjXDwYJ1VTUgny7ehftqKoDEzDB6L3z/MJsNCYB2i6F
          jUICbcI+GshHSdrZ1OQlBmJ0Q2Zj36Jng0FYZPbw0bhIQ6SG3BvaHVHvFJqR4TrQsXDHXlt9FJko
          jll5zKvZgFTNL+z1wBhjX+l3dbYP18XY8iTLajS2lrUoatHWVSGKPMu/NTZx0OeAiQVDgCs+Aj85
          mILSWUL7kLSVtaNdBscPWqtTAljrCBYDX3/vgin9wrgo+Arf5681k3tnUg8IQQcCS1PymJiS+AAe
          jEHTkHOmkWDSEsnHaeeDx3ftYmiWs2qSoetuPEJwVtsrv8zDcew652mTNBoV+x785wweGLtPF4j+
          XUtsSON4WFxhB9FMLvBAzuNWC2E/oAeKCc5/ZTOafJibd8738Pi/cTnlrcNP/aeZb07LyaRIjq+B
          h+ec3NAM254+WpkWk1TrAK1ZHk9MJ7IK0nZ31EI8/Y9vXs8qW4K8oXoUZpP0ufrftyK+w7+jXff1
          EzM5ArMhLlazYsDd6++RQAHBSH8/3P8CAAD//wMAhQO6aMgEAAA=
      headers:
        CF-RAY:
          - 9472cb019ad9433e-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:27 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=WtTUoE6YrHiOeJzkV_hl69OWeRqm715jPbx69WN57Yw-1748488167-*******-utal_YD9oicwIv2autnKRz4TbDXJBFO1KrWjEkd9S0bIFx49vU.ac4e1ypUAklFwsW1Tb6scSBerX8YuWWHuxlTTgI9sMvm9mRLsdPqW9bA;
            path=/; expires=Thu, 29-May-25 03:39:27 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=mD8lr_Yu2M5EC1wPyAFxVEz648jOHHyWmqRwLqw0XUU-1748488167379-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "627"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999959"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_98e3cefc129761545e956f10ca19b950
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","instructions":"Just
        the number please"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "89"
        content-type:
          - application/json
        cookie:
          - __cf_bm=WtTUoE6YrHiOeJzkV_hl69OWeRqm715jPbx69WN57Yw-1748488167-*******-utal_YD9oicwIv2autnKRz4TbDXJBFO1KrWjEkd9S0bIFx49vU.ac4e1ypUAklFwsW1Tb6scSBerX8YuWWHuxlTTgI9sMvm9mRLsdPqW9bA;
            _cfuvid=mD8lr_Yu2M5EC1wPyAFxVEz648jOHHyWmqRwLqw0XUU-1748488167379-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RTW27jMAz8zykEfTcL21FiO0foFYqFQUt0oq0sGRJVtChy94XlR5zd9CdwhuRw
          OKS+d4xxrfiZcY9haE7VoZQdlvURsyqvK1DV4VQfCylkVpZFldXy0B2Lrj7WqurEgb+MBK79g5IW
          EmcDTrj0CISqgTGWl6ISVZWfyhQLBBTDWCNdPxgkVFNRC/L94l20o6oOTMAEo/fO8zOz0ZgEaLsU
          NgoJtAmP0UA+StLOpiavMRCjKzIb+xY9GwzCIrOHz8ZFGiI15N7RPhD1TqEZGS4D7YXb99rqfZEV
          Yp+V+7yaDUjV/Mzedowx9p1+V2f7cFmMrfK8FaOxrWoPXXZSohS5FKf2qbGJg74GTCwYAlzwHvjJ
          wRSUzhLau6StrAfaZXD8pLU6JYC1jmAx8O33QzClnxkvBF/h2/y1ZnLvTOoBIehAYGlKHhNTEh/A
          gzFoGnLONBJMWiL5OO188PihXQzNclZNMnTdjUcIzmp74ed5OI5d5zxtkkajYt+D/5rBHWO36QLR
          f2iJDWkcD4sr7CCayQUeyHncaiHsB/RAMcH5r2xGkw9z8875Hu7/Ny6nvHX4qf8089VpOZkUyfE1
          cPeckxuaYdvTRyvTYpJqHaA1y+OJ6URWQdo+HHVRvPyPb17PKluCvKK6F2aT9Ln637dSPMOf0a77
          +omZHIHZEIvVrBjw4fX3SKCAYKS/7W5/AQAA//8DADqyKq/IBAAA
      headers:
        CF-RAY:
          - 9472cb06ef9b433e-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:28 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "581"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999959"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_0cb0309b702c5eb0a192dc59212bbae6
      status:
        code: 200
        message: OK
  - request:
      body: '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","text":{"format":{"type":"json_schema","strict":true,"name":"NumberAnswer","schema":{"properties":{"value":{"title":"Value","type":"integer"},"reasoning":{"title":"Reasoning","type":"string"}},"required":["value","reasoning"],"title":"NumberAnswer","type":"object","additionalProperties":false}}}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "346"
        content-type:
          - application/json
        cookie:
          - _cfuvid=mD8lr_Yu2M5EC1wPyAFxVEz648jOHHyWmqRwLqw0XUU-1748488167379-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.70.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.70.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RUTW/bMAy951cIOieFnaSN41v/wDDssEszGLREu2plyZOorkWR/z5YThw5TS/5
          4CMp8vGRnwvGuJK8ZNyh76uHoqhrIR8ehMyKfL+vdw1KuROwz3cbcd9k2KybzRayrcAm3zZ8OSSw
          9QsKOiexxuNoFw6BUFYwYPnufrMvik2eRcwTUPBDjLBdr5FQjkE1iNfW2WCGqhrQHqMZnbOOl8wE
          raNBmXNgJZFAaT9HPbkgSFkzs3fwXtlAfaCK7Ct+BclaXQnQ83SdlaiHYtueVlu76pRRq3W23q6y
          3SovTjTEvLxkTwvGGPuMnxO/nW/P9O72dV4M9BZYy+z+YVvLHQq52d+kN+agjx5jFvQeWrwA3/EY
          QWENobmUlJY1S3umBN9pio4OYIwlONP49GcGatv2ztY3kJioZPzzwN9ABzzwcr1dHrhD8NYo0x54
          eeCPUirTsnzNwMjhq1Vv6BkwsgSa2Yatt3cHfuRT6uPp1/Qad1bHDsB75QkMjc6DY3TiPTjQGvV8
          suTCqKve4ZuywVdn6VZxXNPke2e7nioB4hmrV/xIsakbXp5o5dg01lHiNIwodB24c+SCseO4AdAg
          fVRKoiHVKJyp26N7UwIrGu1cYgNBj8PhnqzDtAnCrkcHFKI5v8tO1jiEU2WNdR1c/ifDf/HWVF48
          YwcX6Uj0wql+mPysG8a4gS7G/Qhdje7R+H/oEkGOicpEaAOJPTpS6Gd2xkZ1XBmH0hSNY/0d8eUV
          eipcGcIWHU/Q40yGXwf09YVfk883r3hyA5o+srjxHHf4NyiHcrZtU4vf1DVZk/251HaT4XRtx8Ob
          ICClGmYG+mfKebyji6uaY2fxbg9CmtZmVOe4Lc9WiXG9Alk+AZeN52T7KrkD2WTsUyW6YASctMSl
          8lDr870P8Z5NMlVmdpvv98uv9uTgT2KOCyovgdki6ZVfn/z15hZwK+80qu9Sx1t1AYv1RGHw853u
          kEACxd04Lo7/AQAA//8DAHTNXX18BwAA
      headers:
        CF-RAY:
          - 967f53dbecab159c-SJC
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 31 Jul 2025 18:58:32 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=lkeCiRquYCqgiu623Z6irUAILQyVnYiSNXREMlvbNvU-1753988312-*******-3wYvElBmEIyNAIBM8tphRiDq0ikOlZgfVJo46fL3aSXDNGFmV6Nro7Uoeku7SlTiGgA1JIUNX6Bmz4R1Gc5YpzBQmHzmAiCUQTzWIpNROlw;
            path=/; expires=Thu, 31-Jul-25 19:28:32 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=yWKihNSpBvc.c4a.wglI7VJ8BoWki0lAjHeToYfyouE-1753988312112-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "1680"
        openai-project:
          - proj_vsCSXafhhByzWOThMrJcZiw9
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-decorator-operation:
          - tasksapi.openai.svc.cluster.local:8081/*
        x-envoy-upstream-service-time:
          - "1689"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999922"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_0dfad232b9e4fb11521291a94f39c6b6
      status:
        code: 200
        message: OK
  - request:
      body: '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","text":{"format":{"type":"json_schema","strict":true,"name":"NumberAnswer","schema":{"properties":{"value":{"title":"Value","type":"integer"},"reasoning":{"title":"Reasoning","type":"string"}},"required":["value","reasoning"],"title":"NumberAnswer","type":"object","additionalProperties":false}}}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "346"
        content-type:
          - application/json
        cookie:
          - _cfuvid=yWKihNSpBvc.c4a.wglI7VJ8BoWki0lAjHeToYfyouE-1753988312112-*******-604800000;
            __cf_bm=lkeCiRquYCqgiu623Z6irUAILQyVnYiSNXREMlvbNvU-1753988312-*******-3wYvElBmEIyNAIBM8tphRiDq0ikOlZgfVJo46fL3aSXDNGFmV6Nro7Uoeku7SlTiGgA1JIUNX6Bmz4R1Gc5YpzBQmHzmAiCUQTzWIpNROlw
        host:
          - api.openai.com
        user-agent:
          - AsyncOpenAI/Python 1.70.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - async:asyncio
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.70.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RVTW/bMAy951cIOidD7LqNk9v+wDDssEszGLREO2plyZWofqDIfx8sJ46cppfA
          4RMp8vGR+lwwxpXkO8Yd+r56KMu6boq75v5BlNl2C5t7IWBTQCHFptmu18VDmQGIjRQo5Fbw5RDA
          1k8o6BzEGo+jXTgEQlnBgGWb+7ttWW7vsoh5Agp+8BG26zUSytGpBvHcOhvMkFUD2mM0o3PW8R0z
          QetoUObsWEkkUNrPUU8uCFLWzOwdvFc2UB+oIvuMX0GyVlcC9DxcZyXqIdm2p1VhV50yapWv82K1
          3qyy8kRDjMt37HHBGGOf8Xfit/Ptmd6irLdFpLdp6rsyz7OsFlBmDzfpjTHoo8cYBb2HFi/AdzxG
          UFhDaC4ppWnNwp4pwXeavOMBMMYSnGl8/DcDtW17Z+sbSAy0Y/xzz19BB9zzXV4s99wheGuUafd8
          t+dZznodPMtyhi8BtGd5sWRvByUOTHkmQIugBwWx+oOBlMq0jA7I6M0yE7oanWdkW6QDuh97fuRT
          CsfT15QVd1bHSsF75QkMjYeHg/EQ78GB1qjnCiAXRv31Dl+VDb46S7yKbZ0U0jvb9VQJEAesnvEj
          xaaq+e5EP8emsY6SQ0MrQ9eBO3suGDuOkwIN0kelJBpSjcLZFHh0r0pgRaOdS2wg6LGJ3JN1mBZB
          2PXogEI0Zz/WJ2ts1imzxroOLv8TkTx5ayovDtjBRWISvXCqHxQyq4YxbqCLfr9iq34a/4YuEe4Y
          aJcIciCxR0cK/czO2KiiK+OQmqKxrX8jvrxCT4krQ9ii4wl6nMn1a4O+3vBnOvPNLZ7cgKaXLG5c
          xx2+BOVQzqZyKvGbvCZrMmeX3G4ynI73uKATZJimoWegf6ecx327uMo5Vhb3+yCkaWxGdY7TcrBK
          jOMVyPIJuGwGTravkn2xnox9qkQXjICTlrhUHmp9fhdC3HuTTJWZ7fD77fKrPXkYJjHHAZUXx/Ui
          qZVfPw15eQu4FXdq1XehyRLoC1huJgqDn890hwQSKM7GcXH8DwAA//8DADFQqW6kBwAA
      headers:
        CF-RAY:
          - 967f630858ef2522-SJC
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 31 Jul 2025 19:08:53 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "1248"
        openai-project:
          - proj_vsCSXafhhByzWOThMrJcZiw9
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-decorator-operation:
          - tasksapi.openai.svc.cluster.local:8081/*
        x-envoy-upstream-service-time:
          - "1268"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999922"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_6806eb7c8c8dc661df5873167c5c980e
      status:
        code: 200
        message: OK
version: 1
