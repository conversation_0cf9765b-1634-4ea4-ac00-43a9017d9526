interactions:
  - request:
      body:
        '{"messages": [{"role": "user", "content": "What''s 12 + 12?"}], "model":
        "gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "86"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJBb9QwEIXv+RXWXNlUiderbfdGj/SA4IJUVEWuPUlcHNvYExZU7X9H
          TrabFIrEJYf55k3eG89zwRgYDQcGqpekhmDL2/gl3d5X20/HnaW7u/cfno497+7lx89JHGGTFf7x
          CRW9qK6UH4JFMt7NWEWUhHlqvd/x6mYvxH4Cg9dos6wLVApfDsaZkldclNW+rK/P6t4bhQkO7GvB
          GGPP0zf7dBp/woFVm5fKgCnJDuFwaWIMore5AjIlk0g6gs0ClXeEbrJec/aO1Zzh91HaxLi4WjdG
          bMcks1k3WrsC0jlPMoedLD6cyeliyvouRP+Y/pBCa5xJfRNRJu+ygUQ+wERPBWMPU/jxVR4I0Q+B
          GvLfcPpdLeZxsKx8gddnRp6kXcqcb94Y1mgkaWxa7Q6UVD3qRbksWo7a+BUoVpH/9vLW7Dm2cd3/
          jF+AUhgIdRMiaqNe513aIuZ7/FfbZcWTYUgYfxiFDRmM+Rk0tnK085VA+pUIh6Y1rsMYoplPpQ3N
          VsidkHizVVCcit8AAAD//wMA0gHr9zgDAAA=
      headers:
        CF-RAY:
          - 95cb00386a5a14f6-LAX
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Wed, 09 Jul 2025 21:44:08 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=LhJr7iTKhj3HkuIqsYYQzHnLMYlAsrPO_8DONvOoa1s-1752097448-*******-3QZBiExLtV3A83cyPK4CeYqjpSygQ6uYDZ0v7kYNlmEHZIOldYiM088zk99s107ShpZgiHzSJwcw0Fviy3roAk.Ifv1OH0UzhIx2VTAKaS4;
            path=/; expires=Wed, 09-Jul-25 22:14:08 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=1FpkTfHv2KC0I5jKg65yXzjPU21dBkiuyT323kob89c-1752097448243-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "319"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "322"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_0c9ffafb565ae4556d4374eaba259452
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"messages": [{"role": "user", "content": "What''s 12 + 12?"}], "model":
        "gpt-4o-mini"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "86"
        content-type:
          - application/json
        cookie:
          - __cf_bm=LhJr7iTKhj3HkuIqsYYQzHnLMYlAsrPO_8DONvOoa1s-1752097448-*******-3QZBiExLtV3A83cyPK4CeYqjpSygQ6uYDZ0v7kYNlmEHZIOldYiM088zk99s107ShpZgiHzSJwcw0Fviy3roAk.Ifv1OH0UzhIx2VTAKaS4;
            _cfuvid=1FpkTfHv2KC0I5jKg65yXzjPU21dBkiuyT323kob89c-1752097448243-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.92.3
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.92.3
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600.0"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/chat/completions
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//jJJPb9QwEMXv+RSWr2yqxOtq/xxZcQIhTnBAVeS1J4nB8Rh7gkDVfnfk
          ZLtJS5G45DC/eZP3xvNYMMat4UfGda9ID8GVb+OXdHq3/STfG1F9FP2Hz1tvxEnWJzyc+SYr8PwN
          ND2p7jQOwQFZ9DPWERRBnlrv7kV12Em5n8CABlyWdYFKieVgvS1FJWRZ7cp6f1X3aDUkfmRfC8YY
          e5y+2ac38IsfWbV5qgyQkuqAH29NjPGILle4SskmUp74ZoEaPYGfrNeCvWG1YPBjVC4xIe/WjRHa
          Mals1o/OrYDyHknlsJPFhyu53Ew57ELEc3oh5a31NvVNBJXQZwOJMPCJXgrGHqbw47M8PEQcAjWE
          32H6XS3ncXxZ+QL3V0ZIyi1lITavDGsMkLIurXbHtdI9mEW5LFqNxuIKFKvIf3t5bfYc2/ruf8Yv
          QGsIBKYJEYzVz/MubRHyPf6r7bbiyTBPEH9aDQ1ZiPkZDLRqdPOV8PQ7EQxNa30HMUQ7n0obmq1U
          91LBYat5cSn+AAAA//8DAN/D0ms4AwAA
      headers:
        CF-RAY:
          - 95cb003c2e2414f6-LAX
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Wed, 09 Jul 2025 21:44:09 GMT
        Server:
          - cloudflare
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        access-control-expose-headers:
          - X-Request-ID
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "675"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-upstream-service-time:
          - "693"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999993"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_8eb436911ec46aaf3095f96c82730abe
      status:
        code: 200
        message: OK
version: 1
