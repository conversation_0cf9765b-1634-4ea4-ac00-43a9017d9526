interactions:
  - request:
      body: '{"model": "gpt-4o-mini", "input": "What''s 12 + 12?", "stream": true}'
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "68"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - litellm/1.74.0.post1
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: 'event: response.created

          data: {"type":"response.created","sequence_number":0,"response":{"id":"resp_686ee2bc312c819a87159efd24db25c20d1bf9987998b543","object":"response","created_at":1752097468,"status":"in_progress","background":false,"error":null,"incomplete_details":null,"instructions":null,"max_output_tokens":null,"max_tool_calls":null,"model":"gpt-4o-mini-2024-07-18","output":[],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"summary":null},"service_tier":"auto","store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[],"top_logprobs":0,"top_p":1.0,"truncation":"disabled","usage":null,"user":null,"metadata":{}}}


          event: response.in_progress

          data: {"type":"response.in_progress","sequence_number":1,"response":{"id":"resp_686ee2bc312c819a87159efd24db25c20d1bf9987998b543","object":"response","created_at":1752097468,"status":"in_progress","background":false,"error":null,"incomplete_details":null,"instructions":null,"max_output_tokens":null,"max_tool_calls":null,"model":"gpt-4o-mini-2024-07-18","output":[],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"summary":null},"service_tier":"auto","store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[],"top_logprobs":0,"top_p":1.0,"truncation":"disabled","usage":null,"user":null,"metadata":{}}}


          event: response.output_item.added

          data: {"type":"response.output_item.added","sequence_number":2,"output_index":0,"item":{"id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","type":"message","status":"in_progress","content":[],"role":"assistant"}}


          event: response.content_part.added

          data: {"type":"response.content_part.added","sequence_number":3,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"part":{"type":"output_text","annotations":[],"logprobs":[],"text":""}}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":4,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":"12","logprobs":[]}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":5,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":"
          +","logprobs":[]}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":6,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":"
          ","logprobs":[]}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":7,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":"12","logprobs":[]}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":8,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":"
          equals","logprobs":[]}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":9,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":"
          ","logprobs":[]}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":10,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":"24","logprobs":[]}


          event: response.output_text.delta

          data: {"type":"response.output_text.delta","sequence_number":11,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"delta":".","logprobs":[]}


          event: response.output_text.done

          data: {"type":"response.output_text.done","sequence_number":12,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"text":"12
          + 12 equals 24.","logprobs":[]}


          event: response.content_part.done

          data: {"type":"response.content_part.done","sequence_number":13,"item_id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","output_index":0,"content_index":0,"part":{"type":"output_text","annotations":[],"logprobs":[],"text":"12
          + 12 equals 24."}}


          event: response.output_item.done

          data: {"type":"response.output_item.done","sequence_number":14,"output_index":0,"item":{"id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","type":"message","status":"completed","content":[{"type":"output_text","annotations":[],"logprobs":[],"text":"12
          + 12 equals 24."}],"role":"assistant"}}


          event: response.completed

          data: {"type":"response.completed","sequence_number":15,"response":{"id":"resp_686ee2bc312c819a87159efd24db25c20d1bf9987998b543","object":"response","created_at":1752097468,"status":"completed","background":false,"error":null,"incomplete_details":null,"instructions":null,"max_output_tokens":null,"max_tool_calls":null,"model":"gpt-4o-mini-2024-07-18","output":[{"id":"msg_686ee2bc9144819ab006d750e17d312c0d1bf9987998b543","type":"message","status":"completed","content":[{"type":"output_text","annotations":[],"logprobs":[],"text":"12
          + 12 equals 24."}],"role":"assistant"}],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"summary":null},"service_tier":"default","store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[],"top_logprobs":0,"top_p":1.0,"truncation":"disabled","usage":{"input_tokens":14,"input_tokens_details":{"cached_tokens":0},"output_tokens":9,"output_tokens_details":{"reasoning_tokens":0},"total_tokens":23},"user":null,"metadata":{}}}


          '
      headers:
        CF-RAY:
          - 95cb00b7dbc12aa4-LAX
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Wed, 09 Jul 2025 21:44:28 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=tS6sjs7mcjoero8TwOe6k61qdofnkJzrHrabQ4yUYqM-1752097468-*******-WVKoCchkncTTl1G_AD1k7OhEa5M_ElsbcZ3.ObIMnJR6zKP4KA.eqWFh58u9Y.IQrG7R7E355WFBPjItx.9V2689qVnw_QZb0zd.YNtv0n4;
            path=/; expires=Wed, 09-Jul-25 22:14:28 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=wm8dpBRxaQ_NU66txFhVBBvemrWkoRFFhZ2UF5PAjwo-1752097468279-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "41"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-request-id:
          - req_296275fcb31ed143e8a2129040dc0d1f
      status:
        code: 200
        message: OK
  - request:
      body: '{"model": "gpt-4o-mini", "input": "What''s 12 + 12?", "stream": true}'
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate, zstd
        connection:
          - keep-alive
        content-length:
          - "68"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - litellm/1.74.0.post1
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: ""
      headers:
        CF-RAY:
          - 95cb00b7dbc12aa4-LAX
        Connection:
          - keep-alive
        Content-Type:
          - text/event-stream; charset=utf-8
        Date:
          - Wed, 09 Jul 2025 21:44:28 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=tS6sjs7mcjoero8TwOe6k61qdofnkJzrHrabQ4yUYqM-1752097468-*******-WVKoCchkncTTl1G_AD1k7OhEa5M_ElsbcZ3.ObIMnJR6zKP4KA.eqWFh58u9Y.IQrG7R7E355WFBPjItx.9V2689qVnw_QZb0zd.YNtv0n4;
            path=/; expires=Wed, 09-Jul-25 22:14:28 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=wm8dpBRxaQ_NU66txFhVBBvemrWkoRFFhZ2UF5PAjwo-1752097468279-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "41"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-request-id:
          - req_296275fcb31ed143e8a2129040dc0d1f
      status:
        code: 200
        message: OK
version: 1
