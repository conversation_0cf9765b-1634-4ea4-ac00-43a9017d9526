interactions:
  - request:
      body:
        '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","instructions":"Just
        the number please"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "89"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RTQW7jMAy85xWCzs3CVpzayRP6hWJh0BKTaCtLhkQVLYr8fWHZVuzd9hI4Q3I4
          HFJfO8a4VvzMuMcwtM/NoZYXhSVg1ZSnpsHqUEp5PFbHg6wBCnnoFArRiFNz6MqCP40ErvuDkhYS
          ZwNOuPQIhKqFMVbWVVM1TXlsUiwQUAxjjXT9YJBQTUUdyLerd9GOqi5gAiYYvXeen5mNxiRA26Ww
          VUigTdhGA/koSTubmrzEQIxuyGzsO/RsMAiLzB4+WhdpiNSSe0O7IeqdQjMyXAfaV27fa6v3ohDV
          vqj3ZTMbkKr5mb3uGGPsK/1mZ/twzcae6mc5Gtupqi6UKoUSRQOi/tbYxEGfAyYWDAGu+Aj85GAK
          SmcJ7UPSWtaGdhkcPyhXpwSw1hEsBr7+3gRT+plxUfEM3+evnMm9M6kHhKADgaUpeUxMSXwAD8ag
          ack500owaYnk47TzweO7djG0y1m1ydC8G48QnNX2ys/zcBwvF+dplTQaFfse/OcM7hi7TxeI/l1L
          bEnjeFhc4QWimVzggZzHtRbCfkAPFBNc/ipmNPkwN78438Pj/8rllJeHn/pPM9+clpNJkRzPgYfn
          nNzQDuuePlqZFpNU6wCdWR5PTCeSBWm7OWohnv7HV68ny5Ygb6gehcUkfa7+962I7/DvaPO+fmIm
          R2BWxFU2KwbcvP4eCRQQjPT33f0vAAAA//8DAPvvH7/IBAAA
      headers:
        CF-RAY:
          - 9472cacbad828c83-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:18 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=mI1hauJmw2.LoIBdsHg_QC7_NHqZdnOMzWVIW8FsTRQ-1748488158-*******-PfdMmNepTIKHAoMhhN4llNkTT13MFq_85COUdhxqy3ZRC49jL54D1VIubFkA9P6h7l9xUlSX8At.zsM0DfeQZ2ty5TkOlp7EiC.O7LqtTUI;
            path=/; expires=Thu, 29-May-25 03:39:18 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=tPn8I_CbxQE_7n7eR0tfI8z79gYpGXIT.dYXd5DQUl4-1748488158744-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "611"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999959"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_ed1e4b8509568a7c5b182e720020214a
      status:
        code: 200
        message: OK
  - request:
      body:
        '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","instructions":"Just
        the number please"}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "89"
        content-type:
          - application/json
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.82.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.82.0
        x-stainless-raw-response:
          - "true"
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.13.3
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RT0Y6jMAx871dEed6egNIS+gn7C6sTMsG0uQ0JSpzVrlb99xOhpHDXfano2B6P
          x873jjGuOn5m3KEfm5M4VLLv+rxGIfJaiF6Iqjwcu1qcslPWZ3krRFYcM6zqAxwEf5kIbPsHJS0k
          1niccekQCLsGplhelaIUIj/WMeYJKPipRtph1EjYzUUtyPeLs8FMqnrQHiOMzlnHz8wErSOgzFLY
          dEigtN9GPbkgSVkTm7wGT4yuyEwYWnRs1AiLzAE+GxtoDNSQfUezIRpsh3piuIy0L+1+UEbti6wo
          91m1zxcDYjU/s7cdY4x9x9/k7OAvydg6O0Vj264vi2MNFYhjW2L+1NjIQV8jRhb0Hi74CPzkYAxK
          awjNQ9Ja1oZ2GRw/KVXHBDDGEiwGvv3eBGP6mfGi5Am+3b9SJndWxx7gvfIEhubkKTEm8REcaI26
          IWt1I0HHJZIL885Hhx/KBt8sZ9VEQ9NuHIK3RpkLP9+H49j31tEqaTIqDAO4rzu4Y+w2XyC6DyWx
          IYXTYfEOewh6doF7sg7XWgiHER1QiHD+K7uj0Yd78966AR7/Vy7HvDT83H+e+WqVnE0KZHkKPDzn
          ZMdmXPd0wci4mKhaeWj18nhCPJEkSJnNURfFy//46vUk2RLkFbtHYTZLv1f/+1aKZ/gz2rSvn5jJ
          EugVcZnMCh43r39Agg4IJvrb7vYXAAD//wMA5gltyMgEAAA=
      headers:
        CF-RAY:
          - 9472cad0c942159f-EWR
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 29 May 2025 03:09:19 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=QIXfljmZxZub9UwwuM6OT1N99na9ASw8TDtcrRd6Z8Q-1748488159-*******-zbWSSmIJdFD_aTFzj0JP0WZWpbLM0wEDzYNyALtNsO07NwGG62DBhYk_IRdRHZ9Wf2ooX6MZUcYruGgEgAnGeXE7ply4Hwvl3PEANnp5GJk;
            path=/; expires=Thu, 29-May-25 03:39:19 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=F7Q0gtFcXfGctocXEvQSjRs1S7gyxSThlxKpcdlXKG8-1748488159693-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "558"
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999959"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_9593682049a6bc80e64872aee04f3bdf
      status:
        code: 200
        message: OK
  - request:
      body: '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","text":{"format":{"type":"json_schema","strict":true,"name":"NumberAnswer","schema":{"properties":{"value":{"title":"Value","type":"integer"},"reasoning":{"title":"Reasoning","type":"string"}},"required":["value","reasoning"],"title":"NumberAnswer","type":"object","additionalProperties":false}}}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "346"
        content-type:
          - application/json
        cookie:
          - _cfuvid=tPn8I_CbxQE_7n7eR0tfI8z79gYpGXIT.dYXd5DQUl4-1748488158744-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.70.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.70.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAA3RVTW/bMAy951cIOjeD89XYue0PDMMOuyyDQUu0o1aWPInqVhT574PlRJHb9BLH
          fCJFPj7SbwvGuJL8wLhDP9SPZdk0Qu5W27YsV1VViVaIzap4XFdQ4aotNgJhv92VsIMNoOAPYwDb
          PKGgaxBrPE524RAIZQ0jttrvNlVZbooqYp6Agh99hO0HjYRycmpAPHfOBjNm1YL2GM3onHX8wEzQ
          OhqUuTrWEgmU9nPUkwuClDUzew//ahtoCFSTfcaPIFmrawF6Hq63EvWYbDfQcmuXvTJquS7W22Wx
          X67KCw0xLj+wXwvGGHuLv4nf3neJ3rIsi0jvXm6q3bp63AoJj+V9emMMeh0wRkHvocMb8BmPERTW
          EJpbSnlas7BXSvAfJe94AIyxBFcaf/2egdp2g7PNHSQGOjD+duQvoAMe+WG9fThyh+CtUaY78sOR
          f5VSmY6t1gyMHB+dekHPXm1g6y1rUEDwGF+F7RtlkNEJGf21LAb1jGyHdEL35cjPPN1/vvxLKXFn
          dSwTvFeewNB0eDwYD/EBHGiNet5+cmES3+DwRdng66u+69jTJI/B2X6gWoA4Yf2MrzmWSuaHC/cc
          29Y6yg6NfQx9D+7quWDsPI0JtEivtZJoSLUKZyPg0b0ogTVNdi6xhaCnDnJP1mFeBGE/oAMK0bz6
          UlyssVOXzFrreri9Zwp58tbUXpywh5u+JHrh1DDKY1YNY9xAH/2+hb5B99X4v+gy1U6BDpkaRxIH
          dKTQz+yMTRJ6ZxxTUzS19WfEH96hl8SVIezQ8Qw9z7T6sUEfb/iRznxyiyc3ovklizvXcYd/gnIo
          ZyOZSvwkr2TNhuyW212G89metnOGgJRq7Bno7znncdku3uUcK4vLfRRSGptJndO0nKwS03gFsjwB
          t7XAyQ51tiyKZBxyJbpgBFy0xKXy0OjrRyHEpZdkqsxsge+qh4/27KuQxBwHVN4ci0VWK3//XViX
          94B7cVOrPgtNlkDfwHKfKAx+PtM9EkigOBvnxfk/AAAA//8DADJDGZWhBwAA
      headers:
        CF-RAY:
          - 967f53d37fb9ebeb-SJC
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 31 Jul 2025 18:58:30 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=MfH4U1GWQf0HgvfGCN2oeZz30rG272lA840H0ocITl4-1753988310-*******-_2MfZykpsYaiBMLuP6hYF4d3UgjFvRm39iz6QLyl7KiQpHUQQy5kISxKt1Cz8a2u5kNTnEry7BCShyyz2tC1GV4R_AKK6yKQRPkdnSHFZEs;
            path=/; expires=Thu, 31-Jul-25 19:28:30 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=wq6YLS4G9pN7FGqxeyEbqKUCae8GWtIXy1WxAM3P2oQ-1753988310124-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "1051"
        openai-project:
          - proj_vsCSXafhhByzWOThMrJcZiw9
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-decorator-operation:
          - tasksapi.openai.svc.cluster.local:8081/*
        x-envoy-upstream-service-time:
          - "1058"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999922"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_08be9de3dcbf180a2e859fb2097486c5
      status:
        code: 200
        message: OK
  - request:
      body: '{"input":"What''s 12 + 12?","model":"gpt-4o-mini","text":{"format":{"type":"json_schema","strict":true,"name":"NumberAnswer","schema":{"properties":{"value":{"title":"Value","type":"integer"},"reasoning":{"title":"Reasoning","type":"string"}},"required":["value","reasoning"],"title":"NumberAnswer","type":"object","additionalProperties":false}}}}'
      headers:
        accept:
          - application/json
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "346"
        content-type:
          - application/json
        cookie:
          - _cfuvid=F7Q0gtFcXfGctocXEvQSjRs1S7gyxSThlxKpcdlXKG8-1748488159693-*******-604800000
        host:
          - api.openai.com
        user-agent:
          - OpenAI/Python 1.70.0
        x-stainless-arch:
          - arm64
        x-stainless-async:
          - "false"
        x-stainless-lang:
          - python
        x-stainless-os:
          - MacOS
        x-stainless-package-version:
          - 1.70.0
        x-stainless-read-timeout:
          - "600"
        x-stainless-retry-count:
          - "0"
        x-stainless-runtime:
          - CPython
        x-stainless-runtime-version:
          - 3.11.10
      method: POST
      uri: https://api.openai.com/v1/responses
    response:
      body:
        string: !!binary |
          H4sIAAAAAAAAAwAAAP//dFVNj+IwDL3zK6KcYUVLgZbb/oHVag97GVaVm7olM2nSSZz50Ij/vmoK
          JZ1hLqj42Y7z/Ox8LBjjsuYHxi26vtzleVU1G9xXRZ4nBVTNdpcVmyIrcIfFZrdOiiIRu7xep1vc
          QMWXQwJTPaKgaxKjHY52YREI6xIGLNlvN0WeF+kuYI6AvBtihOl6hYT1GFSBeGqt8XqoqgHlMJjR
          WmP5gWmvVDBIfQ0saySQys1RR9YLkkbP7B28lcZT76kk84RfQTJGlQLUPF1nalRDsW1Pq8ysOqnl
          Kl2n2Wq9XyX5hYaQlx/Yw4Ixxj7C78Rv59oLvdk63dbZQG++38J+t2kSkdXbTZbcpTfkoPceQxZ0
          Dlq8Ad/xGEBhNKG+lRSXNUt7pQTfaIoODqC1IbjS+PBvBirT9tZUd5CQ6MD4x5G/gPJ45Ic0Wx65
          RXBGS90e+eHIk5T1yjuWpAyfPSjH0oxVKMA7ZK8n1OzdeCZMV0mNjE7I6NUw7bsKrVsGECwyqGup
          2wGXloXjHCPTIp3Q/jjyM58qO1++pmK5NSoQAM5JR6BpdB4cgxPvwYJSqObCIOtHWfYWX6Txrrwq
          vwzdnoTTW9P1VAoQJyyf8D3GJjL44dIVjk1jLEVOQ4d914G9Ri4YO48DBA3Seylr1CQbibPhcGhf
          pMCSRjuvsQGvxt5yR8ZifAnCrkcL5IM5+bG+WEMPL5U1xnZw+x9p59EZXTpxwg5uyqvRCSv7QTiz
          2zDGNXQh7lfo40/tXtFGeh4THSKdDiT2aEmim9kZG8X1yTiUJmls69+ALz+hl8KlJmzR8gg9z1T8
          tUFfT/gz+XxziiM7oPEhizvHcYvPXlqsZ8M6XfGbuiZrNH632u4yHE/9uLcjZBiloWegfsechzW8
          +FRzuFlY+4OQprEZ1TlOy8lIMY6XJ8Mn4LYwOJm+jNbIejL2sRKt1wIuWuK1dFCp63PhwzqcZCr1
          bLVvi+VXe/ReTGIOA1rfAteL6K7884uxSe8B9/JOrfouNRkCdQOLZKLQu/lMd0hQA4XZOC/O/wEA
          AP//AwBWTkAJuwcAAA==
      headers:
        CF-RAY:
          - 967f62e628f6df9a-SJC
        Connection:
          - keep-alive
        Content-Encoding:
          - gzip
        Content-Type:
          - application/json
        Date:
          - Thu, 31 Jul 2025 19:08:51 GMT
        Server:
          - cloudflare
        Set-Cookie:
          - __cf_bm=7K88wLiwUejSYSpUrbbQTTUHXJchrH_l2kI9fgnxcXs-1753988931-*******-0GVYMjJh__VuGclT3740O0X6qzhwEkxZ73NnDU0yto26W19LY229WoGh8h.VuBySdJvEqCoVFc72gOID6NOkvrw6Lw.BT.hvHaBAQIcmhQI;
            path=/; expires=Thu, 31-Jul-25 19:38:51 GMT; domain=.api.openai.com; HttpOnly;
            Secure; SameSite=None
          - _cfuvid=VbWwIvwEnDviFllW86zIw2EM2IHt6E3y9YGf8UmVJhc-1753988931644-*******-604800000;
            path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None
        Transfer-Encoding:
          - chunked
        X-Content-Type-Options:
          - nosniff
        alt-svc:
          - h3=":443"; ma=86400
        cf-cache-status:
          - DYNAMIC
        openai-organization:
          - braintrust-data
        openai-processing-ms:
          - "5122"
        openai-project:
          - proj_vsCSXafhhByzWOThMrJcZiw9
        openai-version:
          - "2020-10-01"
        strict-transport-security:
          - max-age=31536000; includeSubDomains; preload
        x-envoy-decorator-operation:
          - tasksapi.openai.svc.cluster.local:8081/*
        x-envoy-upstream-service-time:
          - "5181"
        x-ratelimit-limit-requests:
          - "30000"
        x-ratelimit-limit-tokens:
          - "150000000"
        x-ratelimit-remaining-requests:
          - "29999"
        x-ratelimit-remaining-tokens:
          - "149999922"
        x-ratelimit-reset-requests:
          - 2ms
        x-ratelimit-reset-tokens:
          - 0s
        x-request-id:
          - req_e9aad4c6852e5f8a6c7a20d0333cb94f
      status:
        code: 200
        message: OK
version: 1
