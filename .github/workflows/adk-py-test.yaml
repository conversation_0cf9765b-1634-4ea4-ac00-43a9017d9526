name: adk-py

on:
  workflow_call:
    inputs:
      python-version:
        required: true
        type: string
      os:
        required: true
        type: string

jobs:
  test:
    runs-on: ${{ inputs.os }}

    defaults:
      run:
        working-directory: integrations/adk-py

    steps:
      - uses: actions/checkout@v4

      - name: Set up uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: |
            integrations/adk-py/pyproject.toml
            integrations/adk-py/uv.lock

      - name: Install dependencies
        run: |
          uv python install ${{ inputs.python-version }}
          uv sync

      - name: Lint with ruff
        if: ${{ inputs.os == 'ubuntu-latest' }}
        run: |
          uv run ruff check $(git ls-files '*.py' | grep -v 'examples/')

      - name: Run tests
        run: |
          uv run pytest

      - name: Test import
        run: |
          uv run python -c "import braintrust_adk; print('braintrust_adk imported successfully')"
          uv run python -c "from braintrust_adk import setup_braintrust; print('setup_braintrust imported successfully')"
