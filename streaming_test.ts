import { wrap<PERSON>pen<PERSON><PERSON>, initLogger, withCurrent } from "braintrust";
import OpenAI from "openai";

const openai = wrapOpenAI(new OpenAI());

let testnu = "11"

async function main() {
    const logger = initLogger({ projectName: "pedro-repro5019" });
    const step1 = logger.startSpan({
        name: "my root span",
        spanId: "my-root-span-custom-id" + testnu,
        event: { input: "First step" }
    });
    step1.end();
  
    const step1Export = await step1.export();
    
    // setting the span parent using the export string
    const step2 = logger.startSpan({
        name: "my child span", 
        spanId: "my-child-span-custom-id" + testnu,
        parent: step1Export,
        event: { input: "Second step" }
    });
    step2.end();
    
    // manually setting the span parents
    const step3 = logger.startSpan({
    name: "my grandchild span",
    spanId: "my-grandchild-span-custom-id" + testnu, 
    parentSpanIds: {
        spanId: "my-child-span-custom-id" + testnu,
        rootSpanId: "my-root-span-custom-id" + testnu
    },
    event: { input: "Third step" }
    });
    step3.end();

    // this span will be on the same level as the one in step 3
    const step4 = logger.startSpan({
    name: "my other grandchild span",
    spanId: "my-other-grandchild-span-custom-id" + testnu, 
    parentSpanIds: {
        spanId: "my-child-span-custom-id" + testnu,
        rootSpanId: "my-root-span-custom-id" + testnu
    },
    event: { input: "Fourth step" }
    });

    step4.end();

    // Create a streaming chat completion within the step2 span context
    // if you change step2 with step3 or step4 you can make it a child of those spans instead
    await withCurrent(step4, async () => {
        const stream = await openai.chat.completions.create({
            messages: [
            {
                role: "system",
                content: "You are a helpful assistant that provides clear and concise answers.",
            },
            {
                role: "user",
                content: "Explain what machine learning is in 2-3 sentences.",
            },
            ],
            model: "gpt-4o-mini",
            max_tokens: 300,
            temperature: 0.7,
            stream: true, // This enables streaming
            stream_options: {
            include_usage: true,
            },
        });

        // Consume the stream to trigger the completion
        for await (const chunk of stream) {
            // Process chunks if needed, or just consume them, for the example it will just be printed in the console
            if (chunk.choices[0]?.delta?.content) {
                process.stdout.write(chunk.choices[0].delta.content);
            }
        }
    });

    await logger.flush();
}

main().catch(console.error);