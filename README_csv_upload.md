# Braintrust CSV Upload Script

This script uploads the `longmemeval_first_10_matched.csv` file to a Braintrust dataset using the Python SDK.

## Prerequisites

1. **Install Braintrust Python SDK:**
   ```bash
   pip install braintrust
   ```

2. **Set your Braintrust API key:**
   ```bash
   export BRAINTRUST_API_KEY=your_api_key_here
   ```
   
   You can get your API key from the [Braintrust dashboard](https://www.braintrust.dev/).

## Usage

### Basic Usage
Run the script with the default CSV file path:
```bash
python upload_csv_to_braintrust.py
```

### Custom CSV File Path
You can specify a different CSV file path:
```bash
python upload_csv_to_braintrust.py /path/to/your/csv/file.csv
```

## What the Script Does

1. **Reads the CSV file** and parses each row
2. **Processes the data** by:
   - Parsing JSON fields (`input`, `expected`, `metadata`)
   - Converting comma-separated tags to a list
   - Adding CSV metadata fields to the record metadata
3. **Creates a Braintrust dataset** with the name "longmemeval-first-10-matched" in the project "microsoft-longmemeval"
4. **Uploads each record** to the dataset using the `dataset.insert()` method

## CSV Structure Expected

The script expects a CSV with the following columns:
- `input`: JSON string containing the input data
- `expected`: JSON string containing the expected output
- `tags`: Comma-separated string of tags
- `metadata`: JSON string containing metadata
- `created`: Creation timestamp (added to metadata)
- `span_id`: Span ID (added to metadata)
- `__bt_assignments`: Braintrust assignments (added to metadata)

## Dataset Record Format

Each CSV row is converted to a Braintrust dataset record with:
- `input`: Parsed JSON from the input column
- `expected`: Parsed JSON from the expected column  
- `tags`: List of tags from the tags column
- `metadata`: Combined metadata from the metadata column plus additional CSV fields

## Error Handling

The script includes error handling for:
- Missing CSV file
- JSON parsing errors (falls back to string values)
- Individual record upload failures (continues with remaining records)
- Missing API key

## Example Output

```
=== Braintrust CSV Upload Script ===
CSV file: microsoft_repro/longmemeval_first_10_matched.csv
Reading CSV file: microsoft_repro/longmemeval_first_10_matched.csv
Processed row 1
Processed row 2
...
Successfully parsed 10 records from CSV
Creating Braintrust dataset: longmemeval-first-10-matched in project: microsoft-longmemeval
Dataset created successfully!
Dataset ID: dataset_abc123
Dataset name: longmemeval-first-10-matched
Project: microsoft-longmemeval
Uploading 10 records to dataset...
Uploaded record 1/10 with ID: record_xyz789
...
Upload completed!
Successfully uploaded 10 out of 10 records
```

## Customization

You can modify the script to:
- Change the project name, dataset name, or description
- Add custom data processing logic
- Handle different CSV formats
- Add additional metadata fields

The main configuration is in the `upload_csv_to_braintrust()` function parameters.
