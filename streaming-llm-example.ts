#!/usr/bin/env tsx

import OpenAI from "openai";
import { wrap<PERSON>pen<PERSON><PERSON>, initLogger } from "braintrust";

// Initialize Braintrust logger
initLogger({ projectName: "streaming-llm-examples" });

// Create and wrap OpenAI client with Braintrust
const openai = wrapOpenAI(new OpenAI());

async function streamingChatCompletion() {
  console.log("=== Streaming Chat Completion ===\n");
  
  const stream = await openai.chat.completions.create({
    messages: [
      {
        role: "user",
        content: "Write a short story about a robot learning to paint. Make it about 3 paragraphs.",
      },
    ],
    model: "gpt-4o-mini",
    max_tokens: 500,
    stream: true, // Enable streaming
    stream_options: {
      include_usage: true, // Include token usage in the final chunk
    },
  });

  console.log("Streaming response:");
  let fullContent = "";
  
  for await (const chunk of stream) {
    // Handle text deltas
    const content = chunk.choices[0]?.delta?.content || "";
    if (content) {
      process.stdout.write(content);
      fullContent += content;
    }
    
    // Handle usage information (appears in final chunk)
    if (chunk.usage) {
      console.log(`\n\nUsage: ${JSON.stringify(chunk.usage, null, 2)}`);
    }
  }
  
  console.log(`\n\nFull content length: ${fullContent.length} characters\n`);
}

async function streamingWithResponses() {
  console.log("=== Streaming with OpenAI Responses API ===\n");
  
  // Using the responses API with streaming
  const stream = await openai.responses.create({
    model: "gpt-4o-mini",
    input: "Explain quantum computing in simple terms, as if talking to a 10-year-old.",
    stream: true,
  });

  console.log("Streaming response:");
  let eventCount = 0;
  
  for await (const event of stream) {
    eventCount++;
    // The responses API provides structured events
    if (event.output_text) {
      process.stdout.write(event.output_text);
    }
  }
  
  console.log(`\n\nProcessed ${eventCount} events\n`);
}

async function streamingWithEventHandlers() {
  console.log("=== Streaming with Event Handlers ===\n");
  
  const runner = openai.responses
    .stream({
      model: "gpt-4o-mini",
      input: "What are the benefits of renewable energy? List 5 key points.",
    })
    .on("event", (event) => {
      // Handle all events
      console.log(`Event type: ${event.type}`);
    })
    .on("response.output_text.delta", (diff) => {
      // Handle text deltas specifically
      process.stdout.write(diff.delta);
    });

  // Iterate through the stream
  for await (const event of runner) {
    // Additional processing can be done here
  }

  // Get the final response
  const finalResponse = await runner.finalResponse();
  console.log(`\n\nFinal response length: ${finalResponse.output_text?.length || 0} characters\n`);
}

async function streamingWithErrorHandling() {
  console.log("=== Streaming with Error Handling ===\n");
  
  try {
    const stream = await openai.chat.completions.create({
      messages: [
        {
          role: "user",
          content: "Tell me a joke about programming.",
        },
      ],
      model: "gpt-4o-mini",
      max_tokens: 200,
      stream: true,
    });

    console.log("Streaming response:");
    
    for await (const chunk of stream) {
      try {
        const content = chunk.choices[0]?.delta?.content || "";
        if (content) {
          process.stdout.write(content);
        }
        
        // Check for finish reason
        const finishReason = chunk.choices[0]?.finish_reason;
        if (finishReason) {
          console.log(`\n\nStream finished with reason: ${finishReason}`);
        }
      } catch (chunkError) {
        console.error("Error processing chunk:", chunkError);
      }
    }
  } catch (error) {
    console.error("Error creating stream:", error);
  }
  
  console.log("\n");
}

async function main() {
  console.log("🚀 Braintrust TypeScript SDK Streaming Examples\n");
  
  try {
    // Run different streaming examples
    await streamingChatCompletion();
    await streamingWithResponses();
    await streamingWithEventHandlers();
    await streamingWithErrorHandling();
    
    console.log("✅ All streaming examples completed successfully!");
  } catch (error) {
    console.error("❌ Error running examples:", error);
  }
}

// Run the examples
main().catch(console.error);
