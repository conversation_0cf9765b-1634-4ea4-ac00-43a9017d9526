[project]
name = "braintrust-adk"
version = "0.1.1"
description = "Braintrust Google ADK integration"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "braintrust[otel]>=0.2.4",
    "google-adk>=1.9.0",
    "opentelemetry-proto>=1.36.0",
]
license = { text = "Apache-2.0" }
authors = [{ name = "Braintrust", email = "<EMAIL>" }]
keywords = ["braintrust", "google-adk", "adk", "agents", "ai", "llm", "tracing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

[project.urls]
Homepage = "https://www.braintrust.dev"
Repository = "https://github.com/braintrustdata/braintrust-sdk"


[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]

[tool.uv.workspace]
members = ["examples"]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-asyncio>=1.1.0",
    "pytest-vcr>=1.0.2",
    "ruff>=0.12.9",
]

[tool.isort]
profile = "black"
line_length = 120

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v"
asyncio_default_fixture_loop_scope = "function"
