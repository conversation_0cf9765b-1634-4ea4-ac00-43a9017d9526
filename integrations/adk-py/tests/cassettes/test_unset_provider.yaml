interactions:
- request:
    body: '{"contents": [{"parts": [{"text": "What''s the weather like in New York?"}],
      "role": "user"}], "systemInstruction": {"parts": [{"text": "You are a helpful
      assistant that can check weather and time.\n\nYou are an agent. Your internal
      name is \"weather_time_assistant\"."}], "role": "user"}, "tools": [{"functionDeclarations":
      [{"description": "Get weather for a city.", "name": "get_weather", "parameters":
      {"properties": {"city": {"type": "STRING"}}, "required": ["city"], "type": "OBJECT"}},
      {"name": "get_current_time"}]}], "generationConfig": {}}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '548'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
      user-agent:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-client:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-key:
      - REDACTED
    method: POST
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAC/61RTU/CQBC991c0e6aklVLQK5pIVESt3zFkpNPSsN2tu1vBEP672y9oUW/20OzO
        ezPv7byNYZpkDiyIA1AoyYn5qiumuSn+OcaZQqY0UJd0MQWh9tzy2zTOmhJmbK5izkZAaau5whkk
        qOskQjVbIagFCtI5JIGI5C/Nua1YfeXtE1yZz1wsyQFla/x125/f9npEcFrYSXiAtB62rQkkjFks
        F7cIkrOcdudfT3d2CXxGlzxKBX/P3Vp217Zt59jzhq47dAY9p2f3+45RixeyJJMQ4RUq0HuH3RuJ
        HpKkyudLZCOeFXvvD0qhRkwt3KtgxRXQNtLr/JgqT7VmTJvpNYLVzwdardY/e/Ibkej5LVP1jozG
        Kg8t/pOY19YyqmTKsB5QyLhMJcJE52QddW0rpCAXFq7TYigRKFPOJI6DnHefPS5hDOeT8EJ9KDld
        v4xm7o1LjK3xDQawiMUQAwAA
    headers:
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tue, 19 Aug 2025 20:57:55 GMT
      Server:
      - scaffolding on HTTPServer2
      Server-Timing:
      - gfet4t7; dur=511
      Transfer-Encoding:
      - chunked
      Vary:
      - Origin
      - X-Origin
      - Referer
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - '0'
    status:
      code: 200
      message: OK
- request:
    body: !!binary |
      CqQGCpEBCiIKFnRlbGVtZXRyeS5zZGsubGFuZ3VhZ2USCAoGcHl0aG9uCiUKEnRlbGVtZXRyeS5z
      ZGsubmFtZRIPCg1vcGVudGVsZW1ldHJ5CiEKFXRlbGVtZXRyeS5zZGsudmVyc2lvbhIICgYxLjM2
      LjAKIQoMc2VydmljZS5uYW1lEhEKD3Vua25vd25fc2VydmljZRKNBQoSChBnY3AudmVydGV4LmFn
      ZW50EvYEChAlbAy8sSJbFlbqPn2u+/whEggKaMcWm/yfzCIIxdKLNmBuweEqGGV4ZWN1dGVfdG9v
      bCBnZXRfd2VhdGhlcjABOUDe/EOeRl0YQWj1/kOeRl0YSiMKDWdlbl9haS5zeXN0ZW0SEgoQZ2Nw
      LnZlcnRleC5hZ2VudEonChVnZW5fYWkub3BlcmF0aW9uLm5hbWUSDgoMZXhlY3V0ZV90b29sSiEK
      EGdlbl9haS50b29sLm5hbWUSDQoLZ2V0X3dlYXRoZXJKNAoXZ2VuX2FpLnRvb2wuZGVzY3JpcHRp
      b24SGQoXR2V0IHdlYXRoZXIgZm9yIGEgY2l0eS5KQQoTZ2VuX2FpLnRvb2wuY2FsbC5pZBIqCihh
      ZGstYTUzNzNlYjUtYTk3Yy00Yzg2LWI0YTAtN2RhNWExMGUzZmI3SjkKH2djcC52ZXJ0ZXguYWdl
      bnQudG9vbF9jYWxsX2FyZ3MSFgoUeyJjaXR5IjogIk5ldyBZb3JrIn1KQwoZZ2NwLnZlcnRleC5h
      Z2VudC5ldmVudF9pZBImCiQ1OWI3ZmEyZi0zZDNiLTQ1ZjktODg2NC02MDcyYmM5NjU3MmRKYQoe
      Z2NwLnZlcnRleC5hZ2VudC50b29sX3Jlc3BvbnNlEj8KPXsidGVtcGVyYXR1cmUiOiA3MiwgImNv
      bmRpdGlvbiI6ICJzdW5ueSIsICJjaXR5IjogIk5ldyBZb3JrIn1KJAocZ2NwLnZlcnRleC5hZ2Vu
      dC5sbG1fcmVxdWVzdBIECgJ7fUolCh1nY3AudmVydGV4LmFnZW50LmxsbV9yZXNwb25zZRIECgJ7
      fXoAhQEAAQAA
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '807'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.36.0
      authorization:
      - REDACTED
      x-bt-parent:
      - project_name:braintrust-adk-py
    method: POST
    uri: https://api.braintrust.dev/otel/v1/traces
  response:
    body:
      string: ''
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '0'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 19 Aug 2025 20:57:55 GMT
      Via:
      - 1.1 04c167e03225feef015433dd1c009304.cloudfront.net (CloudFront), 1.1 8da9c3f980fab473d8223728e3f1f35c.cloudfront.net
        (CloudFront)
      X-Amz-Cf-Id:
      - Xh0uJ9Byw3ZyDgh48O9Zx40nA8AndbC2syeiIzmUft87suR52EBYWg==
      X-Amz-Cf-Pop:
      - DFW56-P3
      - DFW56-P4
      X-Amzn-Trace-Id:
      - Root=1-68a4e553-2f5eccf712cc3cc151c481a6;Parent=079e427673ff7aa4;Sampled=0;Lineage=1:24be3d11:0
      X-Cache:
      - Miss from cloudfront
      access-control-allow-credentials:
      - 'true'
      access-control-expose-headers:
      - x-bt-cursor,x-bt-found-existing,x-bt-query-plan
      etag:
      - W/"0-2jmj7l5rSw0yVb/vlWAYkK/YBwk"
      vary:
      - Origin
      x-amz-apigw-id:
      - PkjFGEagoAMEtgQ=
      x-amzn-Remapped-content-length:
      - '0'
      x-amzn-RequestId:
      - d8621bba-84bb-4957-8511-eb0a4e1c7dbf
      x-bt-internal-trace-id:
      - 68a4e5530000000078731c5b4c1794a9
    status:
      code: 200
      message: OK
- request:
    body: !!binary |
      CocNCpEBCiIKFnRlbGVtZXRyeS5zZGsubGFuZ3VhZ2USCAoGcHl0aG9uCiUKEnRlbGVtZXRyeS5z
      ZGsubmFtZRIPCg1vcGVudGVsZW1ldHJ5CiEKFXRlbGVtZXRyeS5zZGsudmVyc2lvbhIICgYxLjM2
      LjAKIQoMc2VydmljZS5uYW1lEhEKD3Vua25vd25fc2VydmljZRLwCwoSChBnY3AudmVydGV4LmFn
      ZW50EtkLChAlbAy8sSJbFlbqPn2u+/whEgjF0os2YG7B4SII7JCWS9ZF5j4qCGNhbGxfbGxtMAE5
      IFgfHZ5GXRhBYKouYp5GXRhKIwoNZ2VuX2FpLnN5c3RlbRISChBnY3AudmVydGV4LmFnZW50Si4K
      FGdlbl9haS5yZXF1ZXN0Lm1vZGVsEhYKFGdlbWluaS0yLjAtZmxhc2gtZXhwSkoKHmdjcC52ZXJ0
      ZXguYWdlbnQuaW52b2NhdGlvbl9pZBIoCiZlLWVmMzZhNTE1LTI1MDktNGExMy04MTI1LTFkMmFh
      ZjJmMjQ3ZEorChtnY3AudmVydGV4LmFnZW50LnNlc3Npb25faWQSDAoKc2Vzc2lvbjEyM0pDChln
      Y3AudmVydGV4LmFnZW50LmV2ZW50X2lkEiYKJDAwMjFkOTNmLTBiMTUtNDMzNC05YzBiLTFiZDU3
      NjZjYmU2YUrEBQocZ2NwLnZlcnRleC5hZ2VudC5sbG1fcmVxdWVzdBKjBQqgBXsibW9kZWwiOiAi
      Z2VtaW5pLTIuMC1mbGFzaC1leHAiLCAiY29uZmlnIjogeyJodHRwX29wdGlvbnMiOiB7ImhlYWRl
      cnMiOiB7IngtZ29vZy1hcGktY2xpZW50IjogImdvb2dsZS1hZGsvMS45LjAgZ2wtcHl0aG9uLzMu
      OS4yMSIsICJ1c2VyLWFnZW50IjogImdvb2dsZS1hZGsvMS45LjAgZ2wtcHl0aG9uLzMuOS4yMSJ9
      fSwgInN5c3RlbV9pbnN0cnVjdGlvbiI6ICJZb3UgYXJlIGEgaGVscGZ1bCBhc3Npc3RhbnQgdGhh
      dCBjYW4gY2hlY2sgd2VhdGhlciBhbmQgdGltZS5cblxuWW91IGFyZSBhbiBhZ2VudC4gWW91ciBp
      bnRlcm5hbCBuYW1lIGlzIFwid2VhdGhlcl90aW1lX2Fzc2lzdGFudFwiLiIsICJ0b29scyI6IFt7
      ImZ1bmN0aW9uX2RlY2xhcmF0aW9ucyI6IFt7ImRlc2NyaXB0aW9uIjogIkdldCB3ZWF0aGVyIGZv
      ciBhIGNpdHkuIiwgIm5hbWUiOiAiZ2V0X3dlYXRoZXIiLCAicGFyYW1ldGVycyI6IHsicHJvcGVy
      dGllcyI6IHsiY2l0eSI6IHsidHlwZSI6ICJTVFJJTkcifX0sICJyZXF1aXJlZCI6IFsiY2l0eSJd
      LCAidHlwZSI6ICJPQkpFQ1QifX0sIHsibmFtZSI6ICJnZXRfY3VycmVudF90aW1lIn1dfV19LCAi
      Y29udGVudHMiOiBbeyJwYXJ0cyI6IFt7InRleHQiOiAiV2hhdCdzIHRoZSB3ZWF0aGVyIGxpa2Ug
      aW4gTmV3IFlvcms/In1dLCAicm9sZSI6ICJ1c2VyIn1dfUrtAgodZ2NwLnZlcnRleC5hZ2VudC5s
      bG1fcmVzcG9uc2USywIKyAJ7ImNvbnRlbnQiOnsicGFydHMiOlt7ImZ1bmN0aW9uX2NhbGwiOnsi
      YXJncyI6eyJjaXR5IjoiTmV3IFlvcmsifSwibmFtZSI6ImdldF93ZWF0aGVyIn19XSwicm9sZSI6
      Im1vZGVsIn0sInVzYWdlX21ldGFkYXRhIjp7ImNhbmRpZGF0ZXNfdG9rZW5fY291bnQiOjYsImNh
      bmRpZGF0ZXNfdG9rZW5zX2RldGFpbHMiOlt7Im1vZGFsaXR5IjoiVEVYVCIsInRva2VuX2NvdW50
      Ijo2fV0sInByb21wdF90b2tlbl9jb3VudCI6NTcsInByb21wdF90b2tlbnNfZGV0YWlscyI6W3si
      bW9kYWxpdHkiOiJURVhUIiwidG9rZW5fY291bnQiOjU3fV0sInRvdGFsX3Rva2VuX2NvdW50Ijo2
      M319Sh8KGWdlbl9haS51c2FnZS5pbnB1dF90b2tlbnMSAhg5SiAKGmdlbl9haS51c2FnZS5vdXRw
      dXRfdG9rZW5zEgIYBnoAhQEAAQAA
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '1674'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.36.0
      authorization:
      - REDACTED
      x-bt-parent:
      - project_name:braintrust-adk-py
    method: POST
    uri: https://api.braintrust.dev/otel/v1/traces
  response:
    body:
      string: ''
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '0'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 19 Aug 2025 20:57:56 GMT
      Via:
      - 1.1 5fdab66fca17d62d8fbfe4fdafee8344.cloudfront.net (CloudFront), 1.1 8da9c3f980fab473d8223728e3f1f35c.cloudfront.net
        (CloudFront)
      X-Amz-Cf-Id:
      - cFAjXwfCTJuFezUGNyOWtzJBQRzM5lW7kd9_KAgRYm1aimzbPjRfaw==
      X-Amz-Cf-Pop:
      - DFW56-P3
      - DFW56-P4
      X-Amzn-Trace-Id:
      - Root=1-68a4e553-4785e0b70aafa4d42f47ed82;Parent=712fc54df95c08a7;Sampled=0;Lineage=1:24be3d11:0
      X-Cache:
      - Miss from cloudfront
      access-control-allow-credentials:
      - 'true'
      access-control-expose-headers:
      - x-bt-cursor,x-bt-found-existing,x-bt-query-plan
      etag:
      - W/"0-2jmj7l5rSw0yVb/vlWAYkK/YBwk"
      vary:
      - Origin
      x-amz-apigw-id:
      - PkjFKE4FIAMElEA=
      x-amzn-Remapped-content-length:
      - '0'
      x-amzn-RequestId:
      - 6d85dfcf-a69d-4ebc-ba9f-86f1bfae5f1d
      x-bt-internal-trace-id:
      - 68a4e55300000000598b6a75fc8d98e0
    status:
      code: 200
      message: OK
- request:
    body: '{"contents": [{"parts": [{"text": "What''s the weather like in New York?"}],
      "role": "user"}, {"parts": [{"functionCall": {"args": {"city": "New York"},
      "name": "get_weather"}}], "role": "model"}, {"parts": [{"functionResponse":
      {"name": "get_weather", "response": {"temperature": 72, "condition": "sunny",
      "city": "New York"}}}], "role": "user"}], "systemInstruction": {"parts": [{"text":
      "You are a helpful assistant that can check weather and time.\n\nYou are an
      agent. Your internal name is \"weather_time_assistant\"."}], "role": "user"},
      "tools": [{"functionDeclarations": [{"description": "Get weather for a city.",
      "name": "get_weather", "parameters": {"properties": {"city": {"type": "STRING"}},
      "required": ["city"], "type": "OBJECT"}}, {"name": "get_current_time"}]}], "generationConfig":
      {}}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '802'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
      user-agent:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-client:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-key:
      - REDACTED
    method: POST
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAC/61Ry27CMBC85ytWPhOUBNFAb31KfVBQSelbldssiUWwI9tpQIh/r5MQSNprfbCs
        nfHO7szGAiBflIcspBoVOYZXUwHYlHeBCa6RawPUJVNMqdQHbnU2jbehaFwVn0gQI+RIdYwSGIc7
        zOFZyAUwBSrjfA050zFQ0LhMUVKdSQQxB9+DECOJqLpvnDRab/fv985hICkSLNSWIsSkpm9rApkz
        zlR8j1QJXtCmwXhC9ij9jm5FlErxWexkO13H6Q36ntd3j4b+cDB0fNezau1SlWSKRjhCTY1rdO8N
        MT2WqQ7EAvmZyErX/F6l0zC5hbv+DtdC06QFDZ3On7bq3IiypGl+IxezPk2YXpfGXzwFpGGRbk9V
        e2Q1rPw94z+JuX5bzNpFU6U1Q6lYFUuESxOU7XUde55QFdu4SsuuRKJKBVd4FRa82fhxQUc37qXM
        Xj6y0cS/np7mJw/E2lo/Ov8NKtACAAA=
    headers:
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tue, 19 Aug 2025 20:57:56 GMT
      Server:
      - scaffolding on HTTPServer2
      Server-Timing:
      - gfet4t7; dur=516
      Transfer-Encoding:
      - chunked
      Vary:
      - Origin
      - X-Origin
      - Referer
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - '0'
    status:
      code: 200
      message: OK
- request:
    body: !!binary |
      CpUPCpEBCiIKFnRlbGVtZXRyeS5zZGsubGFuZ3VhZ2USCAoGcHl0aG9uCiUKEnRlbGVtZXRyeS5z
      ZGsubmFtZRIPCg1vcGVudGVsZW1ldHJ5CiEKFXRlbGVtZXRyeS5zZGsudmVyc2lvbhIICgYxLjM2
      LjAKIQoMc2VydmljZS5uYW1lEhEKD3Vua25vd25fc2VydmljZRL+DQoSChBnY3AudmVydGV4LmFn
      ZW50EucNChAlbAy8sSJbFlbqPn2u+/whEgg4wdcOFvnr/SII7JCWS9ZF5j4qCGNhbGxfbGxtMAE5
      sOnqcJ5GXRhBsCkamJ5GXRhKIwoNZ2VuX2FpLnN5c3RlbRISChBnY3AudmVydGV4LmFnZW50Si4K
      FGdlbl9haS5yZXF1ZXN0Lm1vZGVsEhYKFGdlbWluaS0yLjAtZmxhc2gtZXhwSkoKHmdjcC52ZXJ0
      ZXguYWdlbnQuaW52b2NhdGlvbl9pZBIoCiZlLWVmMzZhNTE1LTI1MDktNGExMy04MTI1LTFkMmFh
      ZjJmMjQ3ZEorChtnY3AudmVydGV4LmFnZW50LnNlc3Npb25faWQSDAoKc2Vzc2lvbjEyM0pDChln
      Y3AudmVydGV4LmFnZW50LmV2ZW50X2lkEiYKJDNkNWFmNGJiLTJmZmItNGRhYy1iZTJmLWY5YTIw
      YTI3ZjBiNUrEBwocZ2NwLnZlcnRleC5hZ2VudC5sbG1fcmVxdWVzdBKjBwqgB3sibW9kZWwiOiAi
      Z2VtaW5pLTIuMC1mbGFzaC1leHAiLCAiY29uZmlnIjogeyJodHRwX29wdGlvbnMiOiB7ImhlYWRl
      cnMiOiB7IngtZ29vZy1hcGktY2xpZW50IjogImdvb2dsZS1hZGsvMS45LjAgZ2wtcHl0aG9uLzMu
      OS4yMSIsICJ1c2VyLWFnZW50IjogImdvb2dsZS1hZGsvMS45LjAgZ2wtcHl0aG9uLzMuOS4yMSJ9
      fSwgInN5c3RlbV9pbnN0cnVjdGlvbiI6ICJZb3UgYXJlIGEgaGVscGZ1bCBhc3Npc3RhbnQgdGhh
      dCBjYW4gY2hlY2sgd2VhdGhlciBhbmQgdGltZS5cblxuWW91IGFyZSBhbiBhZ2VudC4gWW91ciBp
      bnRlcm5hbCBuYW1lIGlzIFwid2VhdGhlcl90aW1lX2Fzc2lzdGFudFwiLiIsICJ0b29scyI6IFt7
      ImZ1bmN0aW9uX2RlY2xhcmF0aW9ucyI6IFt7ImRlc2NyaXB0aW9uIjogIkdldCB3ZWF0aGVyIGZv
      ciBhIGNpdHkuIiwgIm5hbWUiOiAiZ2V0X3dlYXRoZXIiLCAicGFyYW1ldGVycyI6IHsicHJvcGVy
      dGllcyI6IHsiY2l0eSI6IHsidHlwZSI6ICJTVFJJTkcifX0sICJyZXF1aXJlZCI6IFsiY2l0eSJd
      LCAidHlwZSI6ICJPQkpFQ1QifX0sIHsibmFtZSI6ICJnZXRfY3VycmVudF90aW1lIn1dfV19LCAi
      Y29udGVudHMiOiBbeyJwYXJ0cyI6IFt7InRleHQiOiAiV2hhdCdzIHRoZSB3ZWF0aGVyIGxpa2Ug
      aW4gTmV3IFlvcms/In1dLCAicm9sZSI6ICJ1c2VyIn0sIHsicGFydHMiOiBbeyJmdW5jdGlvbl9j
      YWxsIjogeyJhcmdzIjogeyJjaXR5IjogIk5ldyBZb3JrIn0sICJuYW1lIjogImdldF93ZWF0aGVy
      In19XSwgInJvbGUiOiAibW9kZWwifSwgeyJwYXJ0cyI6IFt7ImZ1bmN0aW9uX3Jlc3BvbnNlIjog
      eyJuYW1lIjogImdldF93ZWF0aGVyIiwgInJlc3BvbnNlIjogeyJ0ZW1wZXJhdHVyZSI6IDcyLCAi
      Y29uZGl0aW9uIjogInN1bm55IiwgImNpdHkiOiAiTmV3IFlvcmsifX19XSwgInJvbGUiOiAidXNl
      ciJ9XX1K+wIKHWdjcC52ZXJ0ZXguYWdlbnQubGxtX3Jlc3BvbnNlEtkCCtYCeyJjb250ZW50Ijp7
      InBhcnRzIjpbeyJ0ZXh0IjoiVGhlIHdlYXRoZXIgaW4gTmV3IFlvcmsgaXMgc3Vubnkgd2l0aCBh
      IHRlbXBlcmF0dXJlIG9mIDcyIGRlZ3JlZXMuXG4ifV0sInJvbGUiOiJtb2RlbCJ9LCJ1c2FnZV9t
      ZXRhZGF0YSI6eyJjYW5kaWRhdGVzX3Rva2VuX2NvdW50IjoxNywiY2FuZGlkYXRlc190b2tlbnNf
      ZGV0YWlscyI6W3sibW9kYWxpdHkiOiJURVhUIiwidG9rZW5fY291bnQiOjE3fV0sInByb21wdF90
      b2tlbl9jb3VudCI6NzMsInByb21wdF90b2tlbnNfZGV0YWlscyI6W3sibW9kYWxpdHkiOiJURVhU
      IiwidG9rZW5fY291bnQiOjczfV0sInRvdGFsX3Rva2VuX2NvdW50Ijo5MH19Sh8KGWdlbl9haS51
      c2FnZS5pbnB1dF90b2tlbnMSAhhJSiAKGmdlbl9haS51c2FnZS5vdXRwdXRfdG9rZW5zEgIYEXoA
      hQEAAQAA
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '1944'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.36.0
      authorization:
      - REDACTED
      x-bt-parent:
      - project_name:braintrust-adk-py
    method: POST
    uri: https://api.braintrust.dev/otel/v1/traces
  response:
    body:
      string: ''
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '0'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 19 Aug 2025 20:57:57 GMT
      Via:
      - 1.1 bbbf5b005219c1aba4258ed2665d16aa.cloudfront.net (CloudFront), 1.1 8da9c3f980fab473d8223728e3f1f35c.cloudfront.net
        (CloudFront)
      X-Amz-Cf-Id:
      - QOR6R3mRKmbH_QlTvCVAtuf_jkr2IpHWggVsQgWgOb0J8OE0xauxAA==
      X-Amz-Cf-Pop:
      - DFW56-P3
      - DFW56-P4
      X-Amzn-Trace-Id:
      - Root=1-68a4e554-797767c74080531317377452;Parent=442282e119d3d8d6;Sampled=0;Lineage=1:24be3d11:0
      X-Cache:
      - Miss from cloudfront
      access-control-allow-credentials:
      - 'true'
      access-control-expose-headers:
      - x-bt-cursor,x-bt-found-existing,x-bt-query-plan
      etag:
      - W/"0-2jmj7l5rSw0yVb/vlWAYkK/YBwk"
      vary:
      - Origin
      x-amz-apigw-id:
      - PkjFTFNJoAMEsBQ=
      x-amzn-Remapped-content-length:
      - '0'
      x-amzn-RequestId:
      - ad861edf-5852-4890-8738-9ed39a7d8300
      x-bt-internal-trace-id:
      - 68a4e554000000000063af3b1c331d65
    status:
      code: 200
      message: OK
- request:
    body: !!binary |
      CpICCpEBCiIKFnRlbGVtZXRyeS5zZGsubGFuZ3VhZ2USCAoGcHl0aG9uCiUKEnRlbGVtZXRyeS5z
      ZGsubmFtZRIPCg1vcGVudGVsZW1ldHJ5CiEKFXRlbGVtZXRyeS5zZGsudmVyc2lvbhIICgYxLjM2
      LjAKIQoMc2VydmljZS5uYW1lEhEKD3Vua25vd25fc2VydmljZRJ8ChIKEGdjcC52ZXJ0ZXguYWdl
      bnQSZgoQJWwMvLEiWxZW6j59rvv8IRII7JCWS9ZF5j4iCD+46akMNSURKiJhZ2VudF9ydW4gW3dl
      YXRoZXJfdGltZV9hc3Npc3RhbnRdMAE5eJ0YHZ5GXRhBwOq+qZ5GXRh6AIUBAAEAAA==
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '277'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.36.0
      authorization:
      - REDACTED
      x-bt-parent:
      - project_name:braintrust-adk-py
    method: POST
    uri: https://api.braintrust.dev/otel/v1/traces
  response:
    body:
      string: ''
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '0'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 19 Aug 2025 20:57:57 GMT
      Via:
      - 1.1 1d46bfc36e771034d51de88a8ad881aa.cloudfront.net (CloudFront), 1.1 8da9c3f980fab473d8223728e3f1f35c.cloudfront.net
        (CloudFront)
      X-Amz-Cf-Id:
      - HZJJs6NGzecznBJOyRQGveff_hrR1SZbCuUPhPH7fT2HwYmpZrJGmg==
      X-Amz-Cf-Pop:
      - DFW56-P3
      - DFW56-P4
      X-Amzn-Trace-Id:
      - Root=1-68a4e555-7de38eec682edbcc11fd6ed7;Parent=18e38316f75d1563;Sampled=0;Lineage=1:24be3d11:0
      X-Cache:
      - Miss from cloudfront
      access-control-allow-credentials:
      - 'true'
      access-control-expose-headers:
      - x-bt-cursor,x-bt-found-existing,x-bt-query-plan
      etag:
      - W/"0-2jmj7l5rSw0yVb/vlWAYkK/YBwk"
      vary:
      - Origin
      x-amz-apigw-id:
      - PkjFWEdCIAMEDlw=
      x-amzn-Remapped-content-length:
      - '0'
      x-amzn-RequestId:
      - f733b541-d539-4faa-8d83-70c708394b95
      x-bt-internal-trace-id:
      - 68a4e555000000001ef4ebc556ad6bc1
    status:
      code: 200
      message: OK
- request:
    body: !!binary |
      CvABCpEBCiIKFnRlbGVtZXRyeS5zZGsubGFuZ3VhZ2USCAoGcHl0aG9uCiUKEnRlbGVtZXRyeS5z
      ZGsubmFtZRIPCg1vcGVudGVsZW1ldHJ5CiEKFXRlbGVtZXRyeS5zZGsudmVyc2lvbhIICgYxLjM2
      LjAKIQoMc2VydmljZS5uYW1lEhEKD3Vua25vd25fc2VydmljZRJaChIKEGdjcC52ZXJ0ZXguYWdl
      bnQSRAoQJWwMvLEiWxZW6j59rvv8IRIIP7jpqQw1JREqCmludm9jYXRpb24wATkgERYdnkZdGEGA
      dP7AnkZdGHoAhQEAAQAA
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '243'
      Content-Type:
      - application/x-protobuf
      User-Agent:
      - OTel-OTLP-Exporter-Python/1.36.0
      authorization:
      - REDACTED
      x-bt-parent:
      - project_name:braintrust-adk-py
    method: POST
    uri: https://api.braintrust.dev/otel/v1/traces
  response:
    body:
      string: ''
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '0'
      Content-Type:
      - application/x-protobuf
      Date:
      - Tue, 19 Aug 2025 20:57:57 GMT
      Via:
      - 1.1 5fdab66fca17d62d8fbfe4fdafee8344.cloudfront.net (CloudFront), 1.1 8da9c3f980fab473d8223728e3f1f35c.cloudfront.net
        (CloudFront)
      X-Amz-Cf-Id:
      - 5D-vnE4yXMEX_JVpRZaSCyejvFIxnHMl4WxblgQS8hxhhZMvER6nWQ==
      X-Amz-Cf-Pop:
      - DFW56-P3
      - DFW56-P4
      X-Amzn-Trace-Id:
      - Root=1-68a4e555-2b361e5257ec92565b83fd4c;Parent=609d9dcefbee8ffc;Sampled=0;Lineage=1:24be3d11:0
      X-Cache:
      - Miss from cloudfront
      access-control-allow-credentials:
      - 'true'
      access-control-expose-headers:
      - x-bt-cursor,x-bt-found-existing,x-bt-query-plan
      etag:
      - W/"0-2jmj7l5rSw0yVb/vlWAYkK/YBwk"
      vary:
      - Origin
      x-amz-apigw-id:
      - PkjFaHLYoAMErzg=
      x-amzn-Remapped-content-length:
      - '0'
      x-amzn-RequestId:
      - f5652259-2616-4fc6-a688-07e47fdcf7e7
      x-bt-internal-trace-id:
      - 68a4e5550000000039dbcf3ed894e7f0
    status:
      code: 200
      message: OK
version: 1
