#!/usr/bin/env tsx

import { wrapTraced, initLogger, traced } from "./js/src/logger";
import { configureNode } from "./js/src/node";

// Configure Node.js environment first
configureNode();

// Initialize logger
initLogger({ projectName: "pedro-custom-input-demo" });

// Example 1: Normal wrapTraced (logs actual function arguments)
const normalFunction = wrapTraced(
  function processPayment(creditCard: string, amount: number, userId: string) {
    console.log(`Processing $${amount} payment for user ${userId}`);
    return { success: true, transactionId: "txn_123", amount, userId };
  },
  { name: "normalFunction" }
);

// Example 2: Custom input object (overrides function arguments)
const sanitizedFunction = wrapTraced(
  function processPayment(creditCard: string, amount: number, userId: string) {
    console.log(`Processing $${amount} payment for user ${userId}`);
    return { success: true, transactionId: "txn_456", amount, userId };
  },
  { 
    name: "sanitizedFunction",
    event: {
      input: {
        hasCreditCard: true,
        cardLastFour: "****",
        amount: 100, // You could make this dynamic
        userId: "user_sanitized"
      }
    }
  }
);

// Example 3: Custom input string
const stringFunction = wrapTraced(
  function processPayment(creditCard: string, amount: number, userId: string) {
    console.log(`Processing $${amount} payment for user ${userId}`);
    return { success: true, transactionId: "txn_789", amount, userId };
  },
  { 
    name: "stringFunction",
    event: {
      input: "Payment processing request with sanitized sensitive data"
    }
  }
);

async function demonstrateCustomInput() {
  console.log("🔒 Demonstrating custom input logging with wrapTraced\n");

  // Sensitive test data
  const sensitiveCard = "4532-1234-5678-9012";
  const paymentAmount = 99.99;
  const customerId = "customer_john_doe_123";

  await traced(async (span) => {
    console.log("1️⃣  Normal function (DANGER: logs sensitive data):");
    console.log("   Input logged: [creditCard, amount, userId] - exposes credit card!");
    const result1 = await normalFunction(sensitiveCard, paymentAmount, customerId);
    console.log("   Result:", result1);

    console.log("\n2️⃣  Custom input object (SAFE: logs sanitized data):");
    console.log("   Input logged: custom object with sanitized fields");
    const result2 = await sanitizedFunction(sensitiveCard, paymentAmount, customerId);
    console.log("   Result:", result2);

    console.log("\n3️⃣  Custom input string (SAFE: logs description only):");
    console.log("   Input logged: descriptive string");
    const result3 = await stringFunction(sensitiveCard, paymentAmount, customerId);
    console.log("   Result:", result3);

    console.log("\n✅ Demo completed!");
    console.log("\n📊 Check your Braintrust dashboard to see the difference:");
    console.log("   • normalFunction: Shows actual arguments including credit card number");
    console.log("   • sanitizedFunction: Shows safe, sanitized input object");
    console.log("   • stringFunction: Shows descriptive string only");
    
    console.log(`\n🔗 Span URL: ${await span.permalink()}`);
  }, { name: "payment-processing-demo" });
}

// Run the demonstration
demonstrateCustomInput().catch(console.error);
