#!/usr/bin/env tsx

import { wrapOpenA<PERSON>, initLogger, withCurrent } from "braintrust";
import OpenAI from "openai";

const openai = wrapOpenAI(new OpenAI());

async function testSpanHierarchy() {
    const logger = initLogger({ projectName: "pedro-repro5019" });
    
    // Create a parent span
    const parentSpan = logger.startSpan({
        name: "Parent Span",
        spanId: "parent-span-test",
        event: { input: "Testing span hierarchy" }
    });
    
    console.log("Created parent span:", parentSpan.id);
    
    // Create streaming completion within the parent span context
    await withCurrent(parentSpan, async () => {
        console.log("Making OpenAI call within parent span context...");
        
        const stream = await openai.chat.completions.create({
            messages: [
                {
                    role: "user",
                    content: "Say 'Hello from nested span!' in exactly those words.",
                },
            ],
            model: "gpt-4o-mini",
            max_tokens: 50,
            temperature: 0,
            stream: true,
            stream_options: {
                include_usage: true,
            },
        });
        
        let fullResponse = "";
        for await (const chunk of stream) {
            if (chunk.choices[0]?.delta?.content) {
                const content = chunk.choices[0].delta.content;
                fullResponse += content;
                process.stdout.write(content);
            }
        }
        console.log("\nFull response:", fullResponse);
    });
    
    parentSpan.end();
    await logger.flush();
    
    console.log("Test completed. Check Braintrust logs to verify the streaming completion is nested under the parent span.");
}

testSpanHierarchy().catch(console.error);
