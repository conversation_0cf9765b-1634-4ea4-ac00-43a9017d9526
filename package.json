{"repository": "https://github.com/braintrustdata/braintrust-sdk", "license": "MIT", "private": true, "workspaces": ["js", "core/js"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "watch": "turbo run watch", "start": "turbo run start", "lint": "turbo run lint", "clean": "turbo run clean", "test": "turbo run test"}, "devDependencies": {"pyright": "^1.1.404", "turbo": "^2.5.6"}, "overrides": {"zod": "3.25.34", "tsup": "8.4.0"}, "packageManager": "pnpm@8.15.5"}