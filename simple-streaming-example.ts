#!/usr/bin/env tsx

import OpenAI from "openai";
import { wrap<PERSON>pen<PERSON><PERSON>, initLogger } from "braintrust";

// Initialize Braintrust logger with your project name
initLogger({ projectName: "pedro-repro5019" });

// Create and wrap OpenAI client with Braintrust
const openai = wrapOpenAI(new OpenAI());

async function streamingExample() {
  console.log("Starting streaming LLM call...\n");
  
  // Create a streaming chat completion
  const stream = await openai.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are a helpful assistant that provides clear and concise answers.",
      },
      {
        role: "user",
        content: "Explain what machine learning is in 2-3 sentences.",
      },
    ],
    model: "gpt-4o-mini", // You can change this to other models like "gpt-4o", "gpt-3.5-turbo", etc.
    max_tokens: 300,
    temperature: 0.7,
    stream: true, // This enables streaming
    stream_options: {
      include_usage: true, // Include token usage information
    },
  });

  console.log("Response:");
  let fullResponse = "";
  
  // Process the stream
  for await (const chunk of stream) {
    // Extract content from the chunk
    const content = chunk.choices[0]?.delta?.content || "";
    
    if (content) {
      // Print each chunk as it arrives
      process.stdout.write(content);
      fullResponse += content;
    }
    
    // Check if the stream is finished
    const finishReason = chunk.choices[0]?.finish_reason;
    if (finishReason) {
      console.log(`\n\n[Stream finished: ${finishReason}]`);
    }
    
    // Print usage information when available (usually in the last chunk)
    if (chunk.usage) {
      console.log(`\nToken usage: ${JSON.stringify(chunk.usage, null, 2)}`);
    }
  }
  
  console.log(`\n\nComplete response (${fullResponse.length} characters):`);
  console.log(fullResponse);
}

// Run the example
streamingExample().catch((error) => {
  console.error("Error:", error);
});
