autoevals-0.0.130.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
autoevals-0.0.130.dist-info/METADATA,sha256=JVsit2-084unMF0EOammMGeeRL1jquS40QiMVK-8C9U,17226
autoevals-0.0.130.dist-info/RECORD,,
autoevals-0.0.130.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
autoevals-0.0.130.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
autoevals-0.0.130.dist-info/licenses/LICENSE,sha256=Y6zBoivwzUFxMa704WumP3HEOzxXiWnHJVvFRFeuhmE,1072
autoevals-0.0.130.dist-info/top_level.txt,sha256=MZQSi2eRJo7TGRE7szLkiED5EwWSQgL6xGguGetpzyA,10
autoevals/__init__.py,sha256=ReO4ztG4uFxbhOjifz73D1zj4IFyiXNn7OufBxIAybc,3880
autoevals/__pycache__/__init__.cpython-313.pyc,,
autoevals/__pycache__/json.cpython-313.pyc,,
autoevals/__pycache__/list.cpython-313.pyc,,
autoevals/__pycache__/llm.cpython-313.pyc,,
autoevals/__pycache__/moderation.cpython-313.pyc,,
autoevals/__pycache__/number.cpython-313.pyc,,
autoevals/__pycache__/oai.cpython-313.pyc,,
autoevals/__pycache__/partial.cpython-313.pyc,,
autoevals/__pycache__/ragas.cpython-313.pyc,,
autoevals/__pycache__/score.cpython-313.pyc,,
autoevals/__pycache__/serializable_data_class.cpython-313.pyc,,
autoevals/__pycache__/string.cpython-313.pyc,,
autoevals/__pycache__/test_embeddings.cpython-313.pyc,,
autoevals/__pycache__/test_json.cpython-313.pyc,,
autoevals/__pycache__/test_llm.cpython-313.pyc,,
autoevals/__pycache__/test_moderation.cpython-313.pyc,,
autoevals/__pycache__/test_oai.cpython-313.pyc,,
autoevals/__pycache__/test_partial.cpython-313.pyc,,
autoevals/__pycache__/test_ragas.cpython-313.pyc,,
autoevals/__pycache__/test_serializable_data_class.cpython-313.pyc,,
autoevals/__pycache__/test_values.cpython-313.pyc,,
autoevals/__pycache__/value.cpython-313.pyc,,
autoevals/__pycache__/version.cpython-313.pyc,,
autoevals/json.py,sha256=pBiTK8Nh3npADHsonPHnqSByk-H5ljXrxLF604MWseU,7031
autoevals/list.py,sha256=LzIKwjiCm_UBmiJmxxq3wsaNsCf_gSd1OWaFrdjbBjk,3565
autoevals/llm.py,sha256=ZvSHSN6Q-IGS0dvz4t4_uLcS7nHdZ5VpY5uxz4F_bGc,22076
autoevals/moderation.py,sha256=qghWYAKcorz0Ur9zs5QPMOXSUGwijAscys8xTsB2F0I,4357
autoevals/number.py,sha256=v6cGk1nENdPG-AyO4NabS6xsuGp-VjbAu8CpzojnvYQ,1638
autoevals/oai.py,sha256=gTWSHsWLW546SVoFUoag-hiaQFJU4y4-HRrE1w61Oxo,14064
autoevals/partial.py,sha256=zX3cu3Phs88Zw2-HjxYdZ_XZ_eOom8PlCSfCJXi3aWU,822
autoevals/ragas.py,sha256=H1YneCtwRNMlrNCtfrpxG3zdaiv7YOggDcaGfnvpRdY,63218
autoevals/score.py,sha256=TX1abkeUKNkQTxDiLIy3g6r7j9CnTe10OeKiFJB8lDE,2502
autoevals/serializable_data_class.py,sha256=TjlLpUmVAJ_VW7-aiEecBVQ5d3k4bgQsVeJo_5yIAU0,2471
autoevals/string.py,sha256=QHDbvpFzwsgnv7h9yfUOVoHBKCxf2LK5tZbgNUndTao,6857
autoevals/templates/battle.yaml,sha256=m7erX3HM21SOdKx1t9geHVxcUxWiyYXkfH5ksklHkOY,355
autoevals/templates/closed_q_a.yaml,sha256=DPnYFJgrrGvANjv2i0HM8WfRDfvk5obfV9yXXvQ6Ijg,312
autoevals/templates/factuality.yaml,sha256=mrXywlH4IXRsepqnZnIHTPlV5Wl603kbi7qdwtiofvw,1121
autoevals/templates/humor.yaml,sha256=D5ZO-0AMOI7qK_hSG1fDgbnTcFW8sLnAwhTNjNR5vcQ,107
autoevals/templates/possible.yaml,sha256=7MdnqKTRfSFBPhkXFrEIjFP-32-Ac_Qwk-t2N_7Nslg,636
autoevals/templates/security.yaml,sha256=AQgQ17F8FgHrjJzCmOf87yfvC7iiQpV0s9fYvTazouk,109
autoevals/templates/sql.yaml,sha256=cLJkSLnVasdUF72nFiOVsQDnlL4GUcMA4jPVe5iwhPs,1076
autoevals/templates/summary.yaml,sha256=FJJGfyOa_oB1YJyKKk-vKt191cCVW8-Hs5EOXqnXQhk,439
autoevals/templates/translation.yaml,sha256=3HZY_F-IuHV08cWJAPbG8G4ZY6bxHPoKtdyzvu5Yvoc,687
autoevals/test_embeddings.py,sha256=bX6uNWF_Dw9vON9cBNVl4N3hNazWIEHfR2ZcLsDeDRQ,1468
autoevals/test_json.py,sha256=4mD-jtczQkFYQ-eqP83JsZ8fKmiInAr5L3sCpAcwiRs,4701
autoevals/test_llm.py,sha256=3vfg55RP3ipFLMXdECORG8a1Qrc-c40xewTI5Ghd-Xc,14198
autoevals/test_moderation.py,sha256=V_HWA3Pb04Vo5Xcs06BsrBAGFjHMJOKi0vjgcMIwG80,1023
autoevals/test_oai.py,sha256=fNQermTs5QI0l4rpUZdD6O5cQICysHNGbH_27UHmWk0,7607
autoevals/test_partial.py,sha256=Ht-F6D024txLF7ikVJWL9dbn8AC67ZJDli-4cJmgokg,799
autoevals/test_ragas.py,sha256=IwHTOB8hz-zOLB0PfZKLxO7Di2I668wAv_TLdYNusaQ,2238
autoevals/test_serializable_data_class.py,sha256=b04Ym64YtC6GJRGbKIN4J20RG1QN1FlnODwtEQh4sv0,1897
autoevals/test_values.py,sha256=AVPbURyQoReffwzfA1aIX2wVtQvL6h_FoKq9ObqE5Fc,3215
autoevals/value.py,sha256=nsmZx3PmZrh3ixeb3jyffcRC95hgfUQm3hrsB6DbuO8,2498
autoevals/version.py,sha256=Mzo3Vb0c_4GS1NdxSEjBquy-MX41Lp6djm5kjSsnN10,20
