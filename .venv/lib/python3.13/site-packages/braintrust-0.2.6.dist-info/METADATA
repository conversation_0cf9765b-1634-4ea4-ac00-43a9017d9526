Metadata-Version: 2.4
Name: braintrust
Version: 0.2.6
Summary: SDK for integrating Braintrust
Home-page: https://www.braintrust.dev
Author: Braintrust
Author-email: <EMAIL>
Project-URL: Source Code, https://github.com/braintrustdata/braintrust-sdk
Project-URL: Bug Tracker, https://github.com/braintrustdata/braintrust-sdk/issues
Classifier: Programming Language :: Python :: 3
Classifier: Operating System :: OS Independent
Requires-Python: >=3.8.0
Description-Content-Type: text/markdown
Requires-Dist: GitPython
Requires-Dist: requests
Requires-Dist: chevron
Requires-Dist: tqdm
Requires-Dist: exceptiongroup>=1.2.0
Requires-Dist: python-dotenv
Requires-Dist: sseclient-py
Requires-Dist: python-slugify
Requires-Dist: typing_extensions>=4.1.0
Provides-Extra: cli
Requires-Dist: boto3; extra == "cli"
Requires-Dist: psycopg2-binary; extra == "cli"
Requires-Dist: uv; extra == "cli"
Requires-Dist: starlette; extra == "cli"
Requires-Dist: uvicorn; extra == "cli"
Provides-Extra: doc
Requires-Dist: pydoc-markdown; extra == "doc"
Provides-Extra: openai-agents
Requires-Dist: openai-agents; extra == "openai-agents"
Provides-Extra: otel
Requires-Dist: opentelemetry-api; extra == "otel"
Requires-Dist: opentelemetry-sdk; extra == "otel"
Requires-Dist: opentelemetry-exporter-otlp-proto-http; extra == "otel"
Provides-Extra: all
Requires-Dist: boto3; extra == "all"
Requires-Dist: openai-agents; extra == "all"
Requires-Dist: opentelemetry-api; extra == "all"
Requires-Dist: opentelemetry-exporter-otlp-proto-http; extra == "all"
Requires-Dist: opentelemetry-sdk; extra == "all"
Requires-Dist: psycopg2-binary; extra == "all"
Requires-Dist: pydoc-markdown; extra == "all"
Requires-Dist: starlette; extra == "all"
Requires-Dist: uv; extra == "all"
Requires-Dist: uvicorn; extra == "all"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

## Braintrust

A Python library for logging data to Braintrust. `braintrust` is distributed as
a [library on PyPI](https://pypi.org/project/braintrust/). It is open source and
[available on GitHub](https://github.com/braintrustdata/braintrust-sdk/tree/main/py).

### Quickstart

Install the library with pip.

```bash
pip install braintrust
```

Then, run a simple experiment with the following code (replace `YOUR_API_KEY` with
your Braintrust API key):

```python
from braintrust import Eval

def is_equal(expected, output):
    return expected == output

Eval(
  "Say Hi Bot",
  data=lambda: [
      {
          "input": "Foo",
          "expected": "Hi Foo",
      },
      {
          "input": "Bar",
          "expected": "Hello Bar",
      },
  ],  # Replace with your eval dataset
  task=lambda input: "Hi " + input,  # Replace with your LLM call
  scores=[is_equal],
)
```
