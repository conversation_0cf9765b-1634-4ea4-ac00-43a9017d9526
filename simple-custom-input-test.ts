#!/usr/bin/env tsx

import { wrapTraced, initLogger, traced } from "./js/src/logger";
import { configureNode } from "./js/src/node";

// Configure the Node.js environment first (this initializes the global state)
configureNode();

// Initialize logger
initLogger({ projectName: "pedro-custom-input-test" });

// Function with normal input logging (will log actual arguments)
const normalFunc = wrapTraced(
  function processUserData(password: string, email: string, age: number) {
    console.log(`Processing user: ${email}`);
    return { processed: true, email, age };
  },
  { name: "normalFunc" }
);

// Function with custom input (overrides automatic argument logging)
const customInputFunc = wrapTraced(
  function processUserData(password: string, email: string, age: number) {
    console.log(`Processing user: ${email}`);
    return { processed: true, email, age };
  },
  { 
    name: "customInputFunc",
    event: {
      input: {
        email: "<EMAIL>",
        age: 25,
        hasPassword: true,
        passwordLength: 12
      }
    }
  }
);

// Function with string input override
const stringInputFunc = wrapTraced(
  function processUserData(password: string, email: string, age: number) {
    console.log(`Processing user: ${email}`);
    return { processed: true, email, age };
  },
  { 
    name: "stringInputFunc",
    event: {
      input: "Sanitized user data processing request"
    }
  }
);

async function main() {
  console.log("Testing wrapTraced with custom input...\n");

  // Test data with sensitive information
  const sensitivePassword = "my-secret-password-123";
  const userEmail = "<EMAIL>";
  const userAge = 30;

  await traced(async (span) => {
    console.log("1. Normal function (logs actual arguments):");
    const result1 = await normalFunc(sensitivePassword, userEmail, userAge);
    console.log("Result:", result1);
    
    console.log("\n2. Custom input function (logs custom object):");
    const result2 = await customInputFunc(sensitivePassword, userEmail, userAge);
    console.log("Result:", result2);
    
    console.log("\n3. String input function (logs custom string):");
    const result3 = await stringInputFunc(sensitivePassword, userEmail, userAge);
    console.log("Result:", result3);

    console.log("\n✅ All tests completed!");
    console.log("Check your Braintrust dashboard to see the different input logging:");
    console.log("- normalFunc: Shows [\"my-secret-password-123\", \"<EMAIL>\", 30]");
    console.log("- customInputFunc: Shows the sanitized object");
    console.log("- stringInputFunc: Shows the custom string");
    
    console.log(`\nSpan URL: ${await span.permalink()}`);
  }, { name: "main-test" });
}

main().catch(console.error);
