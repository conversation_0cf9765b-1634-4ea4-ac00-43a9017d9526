import { initLogger } from 'braintrust';
import OpenAI from 'openai';

// Mock constants and enums
const AGENT_UNDERLORD_ID = 'test-agent-id';
const ActorType = {
    User: 'user'
};

const AnalyticsEventName = {
    agentApiResponded: 'agent_api_responded'
};

// Mock implementations
const Users = {
    isInternalUserEmail: (email) => email.includes('@braintrust.dev')
};

// Mock functions
async function getBraintrustLogger(request, agentId) {
    const logger = initLogger({
        projectName: 'pedro-repro5019-streaming',
        apiKey: process.env.BRAINTRUST_API_KEY || 'mock-api-key'
    });
    return logger;
}

function getLastUserQuery(messages) {
    return messages?.find(m => m.role === 'user')?.content || 'Hello, world!';
}

function extractActor(request) {
    return {
        type: ActorType.User,
        user: {
            email: '<EMAIL>'
        }
    };
}

// Mock streaming response with async iterator
async function createChatCompletion(payload, useBackups, useAlternativeKey) {
    if (payload.stream) {
        // Create a mock streaming response
        const mockStream = {
            async *[Symbol.asyncIterator]() {
                yield { choices: [{ delta: { content: 'Hello' } }] };
                yield { choices: [{ delta: { content: ' there!' } }] };
                yield { choices: [{ delta: { content: '' } }], finish_reason: 'stop' };
            }
        };
        return mockStream;
    }
    
    // Non-streaming response
    return {
        id: 'chatcmpl-test',
        object: 'chat.completion',
        created: Date.now(),
        model: payload.model || 'gpt-3.5-turbo',
        choices: [{
            index: 0,
            message: {
                role: 'assistant',
                content: 'This is a mock response'
            },
            finish_reason: 'stop'
        }],
        usage: {
            prompt_tokens: 10,
            completion_tokens: 5,
            total_tokens: 15
        }
    };
}

async function handleSseStream(response, request, logger, analyticsContext) {
    console.log('Handling SSE stream...');
    return new ReadableStream({
        start(controller) {
            controller.enqueue('data: {"choices":[{"delta":{"content":"Mock"}}]}\n\n');
            controller.enqueue('data: {"choices":[{"delta":{"content":" streaming"}}]}\n\n');
            controller.enqueue('data: {"choices":[{"delta":{"content":" response"}}]}\n\n');
            controller.enqueue('data: [DONE]\n\n');
            controller.close();
        }
    });
}

function createAgentApiRespondedProps(baseProps, response) {
    return {
        ...baseProps,
        response_tokens: response.usage?.completion_tokens || 0,
        total_tokens: response.usage?.total_tokens || 0
    };
}

function addConversationHeaders(response, conversationEncodedSpan) {
    if (conversationEncodedSpan) {
        response.header('X-Conversation-Span', JSON.stringify(conversationEncodedSpan));
    }
    return response;
}

// Mock request/response objects
const mockRequest = {
    auth: {
        credentials: {
            id: 'test-user-id'
        }
    }
};

const mockResponse = {
    header: function(name, value) {
        console.log(`Header: ${name} = ${value}`);
        return this;
    },
    type: function(contentType) {
        console.log(`Content-Type: ${contentType}`);
        return this;
    }
};

const mockH = {
    response: function(data) {
        return {
            ...mockResponse,
            data: data,
            type: mockResponse.type,
            header: mockResponse.header
        };
    }
};

// Mock analytics
const analytics = {
    trackEvent: function(eventName, props) {
        console.log(`Analytics Event: ${eventName}`, props);
    }
};

// Test both streaming and non-streaming cases
async function reproduceStreamingIssue() {
    console.log('=== Testing STREAMING case ===');
    await testCase(true);
    
    console.log('\n=== Testing NON-STREAMING case ===');
    await testCase(false);
}

async function testCase(isStreaming) {
    const request = mockRequest;
    const h = mockH;
    const logger = console;
    const payload = {
        model: 'gpt-3.5-turbo',
        messages: [
            { role: 'user', content: 'Hello, how are you?' }
        ],
        stream: isStreaming
    };
    const useBackups = false;
    const useAlternativeOpenrouterKey = false;
    const conversationId = 'test-conversation-id';
    const turnId = 'test-turn-id';
    const requestId = 'test-request-id';
    const callSource = 'test-call-source';
    const project = {
        id: 'test-project-id',
        composition_id: 'test-composition-id',
        commit_ref: 'main'
    };
    const baseEventProps = {
        user_id: 'test-user-id',
        conversation_id: conversationId
    };
    
    let conversationEncodedSpan = null;

    // YOUR ORIGINAL CODE (unchanged)
    try {
        const braintrustLogger = await getBraintrustLogger(request, AGENT_UNDERLORD_ID);

        let response;
        if (braintrustLogger) {
            const lastUserQuery = getLastUserQuery(payload.messages);
            const actor = extractActor(request);
            const isInternalUser =
                actor.type === ActorType.User && Users.isInternalUserEmail(actor.user.email);
            let parentSpan = null; // Declare outside the if block
            if (!conversationEncodedSpan) {
                parentSpan = braintrustLogger.startSpan({
                    name: 'User turn',
                    spanId: turnId,
                    type: 'llm',
                    event: {
                        id: turnId,
                    },
                });
                parentSpan.log({
                    input: lastUserQuery,
                    metadata: {
                        user_id: request.auth.credentials.id,
                        conversation_id: conversationId,
                        turn_id: turnId,
                        project_id: project?.id,
                        composition_id: project?.composition_id,
                        commit_ref: project?.commit_ref,
                        model: payload.model,
                        is_internal: isInternalUser,
                    },
                });
                conversationEncodedSpan = await parentSpan.export();
                // DON'T end parentSpan yet - it should outlive its children
            }

            console.log('Creating callSourceSpan with name:', callSource ? `Parent - ${callSource}` : 'unknown_call_source');
            const callSourceSpan = braintrustLogger.startSpan({
                name: callSource ? `Parent - ${callSource}` : 'unknown_call_source',
                spanId: `Parent - ${callSource}`,
                parent: conversationEncodedSpan,
                event: {
                    id: requestId,
                },
            });
            console.log('callSourceSpan created');

            // Export the parent span to get its encoded reference
            const callSourceSpanEncoded = await callSourceSpan.export();
            console.log('callSourceSpan exported');

            // Start the span manually
            console.log('Creating child span...');
            const span = braintrustLogger.startSpan({
                name: callSource ? `Child - ${callSource}` : 'LLM',
                type: 'llm',
                parent: callSourceSpanEncoded, // Use the exported parent span
                event: {
                    id: `child-${requestId}`,
                    input: lastUserQuery,
                    metadata: {
                        conversation_id: conversationId,
                        turn_id: turnId,
                        request_id: requestId,
                        project_id: project?.id,
                        composition_id: project?.composition_id,
                        commit_ref: project?.commit_ref,
                        model: payload.model,
                        user_id: request.auth.credentials.id,
                        is_internal: isInternalUser,
                    },
                },
            });
            console.log('Child span created');

            try {
                // Execute your function
                response = await createChatCompletion(
                    payload,
                    useBackups,
                    useAlternativeOpenrouterKey,
                );

                // Log the output to the span
                span.log({ output: response });
            } catch (error) {
                // Log any errors to the span
                span.log({ error: error.message });
                throw error;
            } finally {
                // Always end the span
                span.end();
                // End the parent span after child completes
                callSourceSpan.end();
                // End the root span after all children complete
                if (parentSpan) {
                    parentSpan.end();
                }
            }
        } else {
            response = await createChatCompletion(
                payload,
                useBackups,
                useAlternativeOpenrouterKey,
            );
        }

        if (payload.stream && Symbol.asyncIterator in response) {
            console.log('Taking STREAMING path...');
            const analyticsContext = callSource
                ? {
                      baseEventProps,
                      analytics,
                  }
                : undefined;

            const sseStream = await handleSseStream(
                response,
                request,
                logger,
                analyticsContext,
            );

            return addConversationHeaders(
                h
                    .response(sseStream)
                    .type('text/event-stream')
                    .header('X-Accel-Buffering', 'no'),
                conversationEncodedSpan,
            );
        }

        console.log('Taking NON-STREAMING path...');
        if (callSource) {
            const responseProps = createAgentApiRespondedProps(
                baseEventProps,
                response,
            );
            analytics.trackEvent(AnalyticsEventName.agentApiResponded, responseProps);
        }

        return addConversationHeaders(h.response(response), conversationEncodedSpan);
    } catch (error) {
        console.error('Error during reproduction:', error);
        throw error;
    }
}

// Run the reproduction
reproduceStreamingIssue()
    .then(() => {
        console.log('\n=== Reproduction completed successfully ===');
    })
    .catch(error => {
        console.error('Reproduction failed:', error);
        process.exit(1);
    });
