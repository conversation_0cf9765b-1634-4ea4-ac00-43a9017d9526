# Braintrust Span Lifecycle Issue Reproduction

This script reproduces the span lifecycle management issues identified in the original code extract.

## Setup

1. Install dependencies:
```bash
npm install
```

2. (Optional) Set your Braintrust API key:
```bash
export BRAINTRUST_API_KEY=your_api_key_here
```

## Running the Reproduction

### Basic reproduction (non-streaming):
```bash
npm start
```

### Streaming and non-streaming test:
```bash
npm run stream
```

### Or run directly:
```bash
node reproduce-issue.js          # Basic reproduction
node reproduce-streaming.js      # Both streaming and non-streaming
```

## Issues Being Reproduced

The script contains your original code unchanged and should demonstrate:

1. **Span Lifecycle Management Issues**: The `callSourceSpan` is created and immediately ended
2. **Inconsistent Span ID Usage**: Using `callSource` as a `spanId` instead of unique identifiers
3. **Potential Memory Leaks**: Spans might not be properly closed if exceptions occur

## Expected Behavior

The script should run and show the span creation/management behavior. Watch for:
- Console output showing span operations
- Any errors related to span ID conflicts
- Memory usage patterns (if running multiple times)

## Notes

- All external dependencies are mocked to focus on the Braintrust span management
- The original code is preserved exactly as provided
- Mock data is used to simulate a realistic execution environment
